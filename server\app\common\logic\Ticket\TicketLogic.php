<?php

namespace app\common\logic\ticket;

use app\common\enum\YesNoEnum;
use app\common\logic\BaseLogic;
use app\common\model\user\User;
use app\common\model\auth\TenantAdmin;
use app\common\model\ticket\Ticket as TicketModel;
use app\common\model\ticket\TicketReply;
use app\common\model\ticket\TicketTemplate;
use app\common\model\ticket\TicketType;
use app\common\model\ticket\TicketStatus;
use app\common\model\ticket\TicketServiceRating;
use app\common\service\FileService;
use app\common\service\ticket\TicketService;
use app\common\service\ticket\TicketProductService;
use app\common\service\ticket\TicketService as commonTicketService;
use app\api\logic\ticket\TicketServiceRatingLogic;
use app\api\logic\UserLogic;
use app\common\enum\notice\NoticeEnum;
use app\common\enum\ticket\TicketEventEnum;
use app\common\model\article\Article;
use app\common\model\auth\TenantAdminDept;
use app\common\model\auth\TenantDept;
use app\common\model\ticket\TicketCcRecord;
use app\common\model\ticket\TicketDispatchRecord;
use app\common\model\ticket\TicketEvent;
use app\common\model\ticket\TicketRegion;
use app\common\model\ticket\TicketStatusTime;
use app\common\model\article\Article as ArticleModel;
use app\common\logic\DistrictLogic as CommonDistrictLogic;
use app\common\logic\Ticket\TicketStatusTimeLogic;
use app\common\model\auth\AdminDept;
use app\common\model\dict\DictData;
use app\common\model\ticket\TicketCompany;
use think\facade\Request;
use think\facade\Db;


/**
 * 工单逻辑层
 * Class TicketLogic
 * @package app\api\logic\ticket
 */
class TicketLogic extends BaseLogic
{

    /**
     * @notes 用户工单数据
     * <AUTHOR>
     * @date 2025/04/08 16:30
     */
    public static function userData($userId){
        //判断用户是否存在
        if(!$userId) {
            self::setError('用户参数错误');
            return [];
        }
        // 获取当前用户的各个状态的工单数量
        $result['statusCounts'] = self::getTicketStuCounts($userId, 'user');
        // 获取当前用户的工单列表
        $result['ticketList'] = self::getUserTicketIndexList($userId);

        $result['todoData'] = UserLogic::userTodoTicketData($userId);// 待办数据

        $userTicketData = UserLogic::userTicketData($userId);
        // //获取用户今日工单数据
        $result['todayData'] = $userTicketData['today'];
        //获取用户本月工单数据
        $result['monthData'] = $userTicketData['month'];
        //获取用户本年工单数据
        $result['yearData'] = $userTicketData['year'];
        //获取用户总工单数据
        $result['totalData'] = $userTicketData['all'];

        return $result;
    }
    /**
     * @notes 用户页面工单数据
     * <AUTHOR>
     * @date 2025/04/08 16:30
     */
    public static function getUserTicketIndexList($userId, $limit = 5)
    {
        return TicketModel::with([
            'ticketType',
            'ticketStatus',
            'product',
            'company',
        ])->where(['user_id' => $userId])
            ->order('id desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }
    /**
     * @notes 技术员页面数据
     * <AUTHOR>
     * @date 2025/04/08 16:30
     */
    public static function staffData($adminId)
    {
        // 判断如果不是技术员，返回错误信息
        if (!$adminId) {
            self::setError('权限不足');
            return [];
        }
        // 获取当前用的各个状态的工单数量
        $result['statusCounts'] = self::getTicketStuCounts($adminId, 'admin');
        // 获取当前用户的工单列表
        $result['ticketList'] = self::getStaffTicketIndexList($adminId);
        //获取 知识库 列表
        $result['knowledgeList'] = [];
        // $result['knowledgeList'] = self::getStaffArticleIndexList();
        //获取 今日数据
        $result['todayData'] = UserLogic::workbenchData($adminId,'today');
        //获取 本月
        $result['monthData'] = UserLogic::workbenchData($adminId,'month');
        return $result;
    }
    /**
     * @notes 技术员页面工单数据
     * <AUTHOR>
     * @date 2025/04/08 16:30
     */
    public static function getStaffTicketIndexList($adminId, $limit = 5)
    {
        return TicketModel::with([
            'ticketType',
            'ticketStatus',
            'product',
            'company',
        ])->where(['assignee_id' => $adminId])
            ->order('id desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }
    /**
     * @notes 技术员页面知识库数据
     * <AUTHOR>
     * @date 2025/04/08 16:30
     */
    public static function getStaffArticleIndexList($limit = 5)
    {
        return ArticleModel::where(['is_show' => 1])
            ->order('sort desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }
    /**状态列表
     * @notes 获取工单状态列表
     * @return array
     * <AUTHOR>
     * @date 2025/04/08 16:25
     */
    public static function statusList(){
        return TicketStatus::where([
            ['delete_time', '=', null] // 未删除状态
        ])->order('sort asc')->select()->toArray();

    }
    /**
     * @notes 获取工单类型列表
     * @return array
     * <AUTHOR>
     * @date 2025/04/28
     */
    public static function getTicketTypes()
    {
        $types = TicketType::where([
            //['is_enabled', '=', YesNoEnum::YES] // 是否启用
        ])->field('id, type_name')
            ->order('sort desc, id desc')
            ->select()
            ->toArray();

        return ['lists' => $types];
    }

    /**
     * @notes 获取工单地区列表
     * @return array
     * <AUTHOR>
     * @date 2025/04/28
     */
    public static function getTicketRegions()
    {
        $regions = TicketRegion::where([])->field('region_code, region_name')
            ->order('update_time desc, id desc')
            ->select()
            ->toArray();

        return ['lists' => $regions];
    }

    /**
     * @notes 获取工单模板
     * @param array $params
     * @return array|mixed
     * <AUTHOR>
     * @date 2025/04/08 16:25
     */
    public static function getTicketTemplateList(array $params)
    {
        $templates = TicketTemplate::where($params)->order('sort desc')
            ->select()
            ->toArray();
        foreach ($templates as &$template) {
            $template['form_data'] = !empty($template['form_data']) ? json_decode($template['form_data'], true) : [];
            if(!empty($template['default_faq_price'])){
                $template['default_faq_price'] = json_decode($template['default_faq_price'], true); // 解析JSON字符串为数组
            }else{
                $template['default_faq_price'] = []; // 如果没有默认价格，则设置为空数组
            }
        }

        return $templates;
    }


    /**productRecentOrder
     * @notes 获取产品最近工单
     * @param array $params
     * @return array|mixed
     * <AUTHOR>
     * @date 2025/04/08 16:25
     */
    public static function productRecentOrder($productId, $limit = 5){
        return TicketModel::with([
            'ticketType',
            'ticketStatus'
        ])->where(['product_id' => $productId])
            ->order('id desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**getrRecentTicketInfo
     * @notes 获取最近工单信息
     * @return array
     * <AUTHOR>
     * @date 2025/04/28
     */
    public static function getRecentTicketInfo($userId)
    {
        $ticket = TicketModel::where(['user_id' => $userId])
            ->field('id, user_id, user_name, user_phone, user_email, latitude, longitude, address, user_company_id,user_region_code')
            ->order('id desc')
            ->findOrEmpty();
        if ($ticket->isEmpty()) {
            return [];
        } else {
            $ticket['region_code_all'] = [];
            $ticket['region_name_all'] = [];
            if (!empty($ticket['user_region_code'])) {
                //查出上三级区域
                $districtAll = CommonDistrictLogic::upAreaData($ticket['user_region_code']);
                if ($districtAll) {
                    $ticket['region_code_all'] = $districtAll['idArr'];
                    $ticket['region_name_all'] = $districtAll['nameArr'];
                }
            }
            //如果有企业id，获取企业名称
            if (!empty($ticket['user_company_id'])) {
                $ticket['user_company_name'] = TicketCompany::where(['id' => $ticket['user_company_id']])->value('company_name');
            } else {
                $ticket['user_company_name'] = '';
            }
            return $ticket->toArray();
        }
    }

    /**
     * @notes 提交工单
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 16:30
     */
    public static function submit($params)
    {

        Db::startTrans();
        try {
            $userId = $params['user_id'];

            $user = User::findOrEmpty($userId);
            if ($user->isEmpty()) {
                throw new \Exception('用户不存在');
            }

            // 获取初始工单状态 (默认为待受理)
            $initialStatus = TicketStatus::where([
                ['is_initial', '=', YesNoEnum::YES]
            ])->findOrEmpty();
            if ($initialStatus->isEmpty()) {
                throw new \Exception('工单状态异常，请联系管理员');
            }

            $nextStatus = TicketStatus::where([['sort', '>', $initialStatus['sort']]])->order('sort asc')->findOrEmpty();
            
            if(!empty($params['assignee_id'])){
                $params['ticket_status_id'] = $nextStatus->id;//如果有处理人  则设置处理中状态
            }else{
                $params['ticket_status_id'] = $initialStatus->id;//如果没有处理人  则设置初始状态
            }

            //如果工单描述为空，截取工单内容前部分作为工单描述，并过来HTML标签
            if (empty($params['ticket_description'])) {
                // $params['ticket_description'] = strip_tags(mb_substr($params['ticket_content'], 0, 50));
            }

            //获取工单模板配置
            // $template = TicketTemplate::findOrEmpty($params['template_id']);
            // if ($template->isEmpty()) {
            //     throw new \Exception('工单模板有误，请联系管理员');
            // }
            // if($template->circulate_component){//抄送组件

            // }

            // 工单基本信息
            $ticket = new TicketModel();
            $ticket->user_id = $userId;
            $ticket->tenant_id = request()->tenantId; // 租户ID, 假设从请求中获取，根据实际情况修改
            //前端传来的数据
            $ticket->product_id = $params['product_id'] ?? 0; //产品id
            $ticket->product_maintenance_state = $params['product_maintenance_state'] ?? 0; // 维保状态 0不确定  1在保 2过保
            $ticket->project_id = $params['project_id'] ?? 0; // 项目id
            $ticket->urgency = $params['urgency'] ?? 1; // 紧急程度
            $ticket->ticket_type_id = $params['ticket_type_id']; //分类id
            $ticket->template_id = $params['template_id'] ?? 0; // 模板id
            $ticket->user_company_id = $params['user_company_id'] ?? $user->company_id; //所属企业id
            $ticket->user_name = $params['user_name'] ?? $user->nickname; //用户姓名
            $ticket->user_phone = $params['user_phone'] ?? $user->mobile; //用户电话
            $ticket->user_email = $params['user_email'] ?? '';
            $ticket->user_region_code = $params['user_region_code'] ?? $user->region_code; //用户区域
            $ticket->address = $params['address'] ?? ''; // 地址
            $ticket->latitude = $params['latitude'] ?? ''; // 纬度
            $ticket->longitude = $params['longitude'] ?? ''; // 经度
            $ticket->related_ticket_id = $params['related_ticket_id'] ?? 0; // 关联工单ID
            $ticket->ticket_code = commonTicketService::generateTicketCode(); // 工单编号
            $ticket->ticket_title = $params['ticket_title']; //工单标题
            $ticket->ticket_description = $params['ticket_description'] ?? '';
            $ticket->ticket_content = $params['ticket_content'] ?? '';
            $ticket->ticket_images = isset($params['ticket_images']) && $params['ticket_images'] ? (is_array($params['ticket_images']) ? json_encode($params['ticket_images']) : $params['ticket_images']) : "";
            $ticket->expected_date = $params['expected_date'] ? strtotime($params['expected_date']) : strtotime(date('Y-m-d')); // 期望上门服务日期
            $ticket->expected_time_period = $params['expected_time_period'] ?? ''; // 期望上门服务时段
            $ticket->source_type = $params['source_type'] ?? 5; // 工单来源类型
            $ticket->ai_analysis = $params['ai_analysis'] ?? ''; // AI分析数据
            $ticket->assignee_id =  $params['assignee_id'] ?? 0; //处理人ID
            $ticket->first_receive_time = isset($params['first_receive_time']) && $params['first_receive_time'] && $ticket->assignee_id ? $params['first_receive_time'] : null; // 首次分配时间
            $ticket->allocate_ticket_time = isset($params['allocate_ticket_time']) && $params['allocate_ticket_time'] && $ticket->assignee_id  ? $params['allocate_ticket_time'] : null; // 分配工单时间
            $ticket->latest_processing_time = isset($params['latest_processing_time']) && $params['latest_processing_time'] && $ticket->assignee_id  ? $params['latest_processing_time'] : null; // 最新处理时间
            $ticket->allocation_status = $ticket->assignee_id ? 1 : 0; // 分配状态 0 未分配 1 已分配
            $ticket->receiver_admin_id = $ticket->assignee_id ?? 0; // 接单的员工
            if($ticket->assignee_id){
                $deptModel = TenantAdminDept::where(['admin_id' => $ticket->assignee_id])->findOrEmpty();
                $ticket->dept_id = $deptModel->dept_id ?? 0; // 处理人部门id
            }else{
                $ticket->dept_id = 0; // 处理人部门id
            }

            //获取标题是否为设置的faq
            $faqPrice = TicketService::getFaqPrice($params['template_id'], $params['ticket_type_id'], $params['ticket_title']);
            if($faqPrice>0){
                $ticket->payment_amount = $faqPrice;
                $ticket->payment_status = 1;
            }else{
                $ticket->payment_amount = 0; //
                $ticket->payment_status = 0; //付费状态
            }

            // 其他字段根据实际情况设置
            $ticket->client_confirm_amount = 0; //客户确认金额
            $ticket->audit_status = 0; //工单审核状态
            $ticket->workflow_audit_id = 0; //流程审核ID
            $ticket->view_status = 0; //查看状态 0 未查看 1 已查看
            $ticket->ticket_action = ''; //工单动作

            $ticket->ticket_status_id = $params['ticket_status_id']; // 默认为初始状态ID
            $ticket->create_time = time();


            $result = $ticket->save();


            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单创建失败');
            }
            //如果我想要返回 工单的id  可以这样写

            $ticket->refresh();

            // 设置新工单ID用于关联记录
            $params['id'] = $ticket->id;

            if ($ticket->assignee_id) { //如果有处理人  则设置处理人

                $prarm_dispatch = [
                    'id' => $ticket->id, //工单id
                    'dispatch_admin_ids' => $ticket->assignee_id, //处理人id
                ];
                $prarm_dispatch_ext = [
                    'dispatcher_id' => 0,
                    'dispatch_time' => time()
                ];
                // 处理协同处理人 实质上就是新增派单记录
                CommonTicketService::handleRelation($prarm_dispatch, 'dispatch_admin_ids', TicketDispatchRecord::class, $prarm_dispatch_ext);
            } else { //如果没有处理人  则工单会进入抢单大厅

            }

            // 写入初始工单状态 `ticket_status_time` 
            $statusTime =  TicketStatusTimeLogic::executeTicketStatusTime($ticket->id, 0, $initialStatus->id, 0, $ticket->tenant_id,$userId);
            if (!$statusTime) {
                //抛出错误
                throw new \Exception(TicketStatusTimeLogic::getError()??'写入初始状态节点失败');
            }


            if($ticket->assignee_id){//如果有处理人  则设置处理中状态
                // 自动派单写入工单状态 `ticket_status_time` 
                $statusTime =  TicketStatusTimeLogic::executeTicketStatusTime($ticket->id, $initialStatus->id, $nextStatus->id, 0, $ticket->tenant_id,0);
                if (!$statusTime) {
                    //抛出错误
                    throw new \Exception(TicketStatusTimeLogic::getError()??'更新状态节点失败');
                }
            }

            

            // 处理抄送人
            CommonTicketService::handleRelation($params, 'cc_admin_ids', TicketCcRecord::class, [
                'cc_time' => time()
            ]);


            // var_dump($ticket);exit;

            // 执行工单创建时的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'ticket_create', 'api', ['previous_status_id' => $initialStatus->id, 'notice_type' => NoticeEnum::ORDER_NEW]);

            
            if($ticket->assignee_id){
                // 执行工单创建时自动派单的智能策略
                commonTicketService::executeSmartPolicy($ticket, 'change_status', 'api', ['previous_status_id' => $initialStatus->id, 'notice_type' => NoticeEnum::ORDER_CHANGE]);
            }


            // // 自动分配处理人 (根据SLA规则和工单配置)
            // self::autoAssignHandler($ticket);

            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::CREATE_TICKET, [
                'user_id' => $userId, //处理人id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' => '用户ID：' . $ticket->user_id . '创建工单', //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);

            // // 发送通知
            // self::sendTicketNotification($ticket, 'create');

            Db::commit();
            return true;
        } catch (\Exception $e) {
            // Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
    /**
     * @notes 获取技术员的工单列表
     * @param array $params
     * @return array
     * <AUTHOR>
     * @date 2025/04/08 16:40
     */
    public static function staffTickets($params)
    {
        $adminId = $params['admin_id']; //获取管理员ID
        // 分页参数处理
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize = isset($params['pageSize']) ? intval($params['pageSize']) : 10;

        if ($adminId) {
            $where = [
                ['dispatch_record.admin_id', '=', $adminId], // 只查询处理人的工单
                ['dispatch_record.delete_time', '=', null], // 未删除的

            ];

            // 按状态筛选
            if (isset($params['status_id']) && $params['status_id'] !== '') {
                $where[] = ['ticket.ticket_status_id', '=', $params['status_id']];
            }

            // 按工单类型筛选
            if (isset($params['type_id']) && $params['type_id'] !== '') {
                $where[] = ['ticket.ticket_type_id', '=', $params['type_id']];
            }
            $sql = TicketModel::alias('ticket')
            ->leftJoin('ticket_type type', 'ticket.ticket_type_id = type.id')
            ->leftJoin('ticket_status status', 'ticket.ticket_status_id = status.id')
            ->leftJoin('ticket_product product', 'ticket.product_id = product.id')
            ->leftJoin('ticket_company company', 'ticket.user_company_id = company.id')
            ->leftJoin('ticket_dispatch_record dispatch_record', 'ticket.id = dispatch_record.ticket_id')
            ->field('ticket.*, 
            type.type_name, 
            status.status_name, status.status_color, 
            product.name as product_name,product.code as product_code,product.brand as product_brand,product.label as product_label,product.model as product_model,product.maintenance_start_time as product_maintenance_start_time,product.maintenance_end_time as product_maintenance_end_time,
            company.company_name, company.company_address, company.company_latitude,company.company_longitude')
            ->group('ticket.id')->where($where);

            // $query = $sql->fetchSql(true)->select();
            // var_dump($query);exit;

            $count = $sql->count();

            $lists = $sql->order('ticket.id desc')
                ->page($page, $pageSize)
                ->select()
                ->toArray();
            
            // 读取工单配置信息
            $ticketConfig = commonTicketService::getTicketConfig($lists[0]['tenant_id'] ?? 0);

            foreach ($lists as &$item) {
                // 格式化图片和文件
                $item['images'] = !empty($item['images']) ? json_decode($item['images'], true) : [];
                if ($item['user_phone']) {
                    $item['user_desensitize_phone'] = desensitize_phone($item['user_phone']);
                } else {
                    $item['user_desensitize_phone'] = '';
                }
                //判断是参与或者是负责
                $item['is_participate'] = 0; // 0 不参与 1 参与
                if ($item['assignee_id'] == $adminId) {
                    $item['is_participate'] = 1; // 1 负责
                }
                // 处理工单动作
                $item['actions'] = self::getStaffActionStatus($item,$ticketConfig);
            }
            unset($item); // 销毁引用变量

            if ($page == 1) {
                $statusCounts = self::getTicketStuCounts($adminId, 'admin');
            } else {
                $statusCounts = [];
            }
            return [
                'statusCounts' => $statusCounts,
                'total' => $count,
                'lists' => $lists,
                'page' => $page,
                'pageSize' => $pageSize
            ];
        } else {
            return [
                'statusCounts' => [],
                'total' => 0,
                'lists' => [],
                'page' => $page,
                'pageSize' => $pageSize
            ];
        }
    }

    /**下个工单状态
     * @param $status
     * @return array
     *  hf_nsz 2025/04/30 16:40
     */
    public static function nextStatus($status){
        if($status['is_final']){//终态
            return [];
        }else{
            return TicketStatus::where([['sort', '>', $status['sort']]])->order('sort asc')->findOrEmpty()->toArray();
        }
    }

    /**
     * @notes 获取工单动作
     * 技术员工单操作按钮状态
     * 改派 修改状态 重启工单 完成 取消按钮  留言回复  紧急程度变更 退出参与 改金额 修改付费状态 催确认 催支付 确认支付
     *  hf_nsz 2025/04/30 16:40
     * @param $ticket
     * @param $adminId
     * @param $ticketConfig
     * @return array
     */
    public static function getStaffActionStatus($ticket,$ticketConfig)
    {

        $item = [];
        $item['config'] = $ticketConfig; // 配置信息
        //当前工单状态
        $STATUS = TicketStatus::where([['id', '=', $ticket['ticket_status_id']]])->findOrEmpty();
        // if ($STATUS->isEmpty()) {
        //     return $item;
        // }
        $item['status'] = $STATUS; // 状态ID

        //下个工单状态
        $NEXT_STATUS = self::nextStatus($STATUS);
        if(!empty($NEXT_STATUS)){
            $item['next_status_id'] = $NEXT_STATUS['id']; // 下个状态ID
        }else{
            $item['next_status_id'] = 0; //  下个状态ID
        }
        $item['next_status'] = $NEXT_STATUS; // 下个状态名称
        

        // 用户评价记录
        $item['rated'] = TicketServiceRatingLogic::detail(['ticket_id' => $ticket['id']]); //已有评价记录; // 评价记录;

        return $item;
    }

    /**
     * @notes 获取我的工单列表
     * @param array $params
     * @return array
     * <AUTHOR>
     * @date 2025/04/08 16:40
     */
    public static function myTickets($params)
    {
        $userId = $params['user_id'];

        $where = [
            ['user_id', '=', $userId]
        ];

        // 按状态筛选
        if (isset($params['status_id']) && $params['status_id'] !== '') {
            $where[] = ['ticket_status_id', '=', $params['status_id']];
        }

        // 按工单类型筛选
        if (isset($params['type_id']) && $params['type_id'] !== '') {
            $where[] = ['ticket_type_id', '=', $params['type_id']];
        }

        // 分页参数处理
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize = isset($params['pageSize']) ? intval($params['pageSize']) : 10;

        $lists = TicketModel::with([
            'ticketType',
            'ticketStatus',
            'assignee' => function ($query) {
                // 只选择需要返回的字段，可根据实际情况修改
                $query->field('id, name, avatar, phone');
            }
        ])
            ->where($where)
            ->order('id desc')
            ->page($page, $pageSize)
            ->select()
            ->toArray();

        $count = TicketModel::where($where)->count();

        // 读取工单配置信息
        $ticketConfig = commonTicketService::getTicketConfig($lists[0]['tenant_id'] ?? 0);

        foreach ($lists as &$item) {
            // 格式化图片和文件
            $item['images'] = !empty($item['images']) ? json_decode($item['images'], true) : [];
            $item['files'] = !empty($item['files']) ? json_decode($item['files'], true) : [];

            // 获取处理人信息
            if (!empty($item['assignee'])) {
                $item['assignee_name'] = $item['assignee']['name'] ?? '';
                $item['assignee_desensitize_name'] = desensitize_name($item['assignee']['name']) ?? '';
                $item['assignee_phone'] = $item['assignee']['phone'] ?? '';
                $item['assignee_desensitize_phone'] = desensitize_phone($item['assignee']['phone']) ?? '';
                $item['assignee_avatar'] = $item['assignee']['avatar'] ?? '';
            } else {
                $item['assignee_name'] = '';
                $item['assignee_phone'] = '';
                $item['assignee_desensitize_name'] = '';
                $item['assignee_desensitize_phone'] = '';
                $item['assignee_avatar'] = '';
            }
            unset($item['assignee']);

            // 处理状态名称和颜色
            if (!empty($item['ticketStatus'])) {
                $item['status_name'] = $item['ticketStatus']['status_name'] ?? '';
                $item['status_color'] = $item['ticketStatus']['status_color'] ?? '';
            } else {
                $item['status_name'] = '';
                $item['status_color'] = '';
            }
            unset($item['ticketStatus']);

            // 处理工单类型名称
            if (!empty($item['ticketType'])) {
                $item['type_name'] = $item['ticketType']['type_name'] ?? '';
            } else {
                $item['type_name'] = '';
            }
            unset($item['ticketType']);
            // 处理工单动作
            $item['actions'] = self::getTicketActionStatus($item,$ticketConfig);
        }
        unset($item); // 销毁引用变量
        if ($page == 1) {
            $statusCounts = self::getTicketStuCounts($userId);
        } else {
            $statusCounts = [];
        }
        return [
            'statusCounts' => $statusCounts,
            'total' => $count,
            'lists' => $lists,
            'page' => $page,
            'pageSize' => $pageSize
        ];
    }
    /**
     * @notes 获取工单动作状态
     *  取消  重启  确认金额 催单 评价 留言回复
     * @param array $ticket
     * @return array
     * <AUTHOR>
     */
    private static function getTicketActionStatus($ticket,$ticketConfig)
    {
        $item = [];
        $item['config'] = $ticketConfig; // 配置信息
        //当前工单状态
        $STATUS = TicketStatus::where([['id', '=', $ticket['ticket_status_id']]])->findOrEmpty();
        // if ($STATUS->isEmpty()) {
        //     return $item;
        // }

        $item['status'] = $STATUS; // 状态ID

        //下个工单状态
        // $NEXT_STATUS = self::nextStatus($STATUS);
        // if(!empty($NEXT_STATUS)){
        //     $item['next_status_id'] = $NEXT_STATUS['id']; // 下个状态ID
        // }else{
        //     $item['next_status_id'] = 0; //  下个状态ID
        // }
        // $item['next_status'] = $NEXT_STATUS; // 下个状态名称

        $rateRecord = TicketServiceRatingLogic::detail(['ticket_id' => $ticket['id']]); //已有评分记录
        $item['rated'] = $rateRecord; // 评价记录;

       
        return $item;
    }
    /**
     * @notes 获取工单列表
     * @param array $params
     * @return array
     * <AUTHOR>
     * @date 2025/04/28
     */
    public static function listTickets($params)
    {
        $where = [];

        // 按状态筛选
        if (isset($params['status_id']) && $params['status_id'] !== '') {
            $where[] = ['ticket_status_id', '=', intval($params['status_id'])];
        }

        // 按是否有处理人筛选
        if (isset($params['assignee_id']) && $params['assignee_id'] !== '') {
            $where[] = ['assignee_id', '=', intval($params['assignee_id'])];
        }

        // 按是否关闭筛选
        if (isset($params['close_status']) && $params['close_status'] !== '') {
            $where[] = ['close_status', '=', intval($params['close_status'])];
        }

        // 按工单类型筛选
        if (isset($params['type_id']) && $params['type_id'] !== '') {
            if ($params['type_id'] > 0) $where[] = ['ticket_type_id', '=', intval($params['type_id'])];
        }

        // 按地区筛选
        if (isset($params['region_code']) && $params['region_code'] !== '') {
            if ($params['region_code'] > 0) $where[] = ['user_region_code', 'like', $params['region_code'] . '%'];
        }

        // 按关键字搜索（标题）
        if (isset($params['keyword']) && $params['keyword'] !== '') {
            $where[] = ['ticket_title', 'like', '%' . $params['keyword'] . '%'];
        }

        // 过滤用户自己发布的工单
        if (isset($params['user_id']) && $params['user_id'] > 0) {
            $where[] = ['user_id', '<>', intval($params['user_id'])];
        }

        // var_dump($where);exit;
        // 分页参数处理
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize = isset($params['pageSize']) ? intval($params['pageSize']) : 10;

        $lists = TicketModel::with(['ticketType', 'ticketStatus', 'assignee'])
            ->where($where)
            ->order('id desc')
            ->page($page, $pageSize)
            ->select()
            ->toArray();

        $count = TicketModel::where($where)->count();

        foreach ($lists as &$item) {
            // 格式化图片和文件
            $item['images'] = !empty($item['images']) ? json_decode($item['images'], true) : [];
            $item['files'] = !empty($item['files']) ? json_decode($item['files'], true) : [];

            // 获取处理人信息
            if (!empty($item['assignee'])) {
                $item['assignee_name'] = $item['assignee']['name'] ?? '';
                $item['assignee_desensitize_name'] = desensitize_name($item['assignee']['name']) ?? '';
                $item['assignee_phone'] = $item['assignee']['phone'] ?? '';
                $item['assignee_desensitize_phone'] = desensitize_phone($item['assignee']['phone']) ?? '';
                $item['assignee_avatar'] = $item['assignee']['avatar'] ?? '';
            } else {
                $item['assignee_name'] = '';
                $item['assignee_phone'] = '';
                $item['assignee_desensitize_name'] = '';
                $item['assignee_desensitize_phone'] = '';
                $item['assignee_avatar'] = '';
            }
            // unset($item['assignee']);

            // 处理状态名称和颜色
            if (!empty($item['ticketStatus'])) {
                $item['status_name'] = $item['ticketStatus']['status_name'] ?? '';
                $item['status_color'] = $item['ticketStatus']['status_color'] ?? '';
            } else {
                $item['status_name'] = '';
                $item['status_color'] = '';
            }
            // unset($item['ticketStatus']);

            // 处理工单类型名称
            if (!empty($item['ticketType'])) {
                $item['type_name'] = $item['ticketType']['type_name'] ?? '';
            } else {
                $item['type_name'] = '';
            }
            // unset($item['ticketType']);

            // 假设可以取消;
            // $canCancel=false;
            // $pandingStatus = TicketStatus::where([['id','=', $item['ticket_status_id']],['status_code','like','%PENDING%']])->findOrEmpty();
            // if($item['allocation_status']==0&&!$pandingStatus->isEmpty()){
            //     $canCancel=true;
            // }
            // $item['canCancel'] = $canCancel; 

            // 假设可以确认金额
            // $canConfirmAmount=false;
            // if($item['payment_status']==1&&$item['client_confirmed_amount']==0){
            //     $canConfirmAmount=true;
            // }
            // $item['canConfirmAmount'] = $canConfirmAmount; 

            // 假设可以关闭;
            // $canClose=false;
            // $finalStatus = TicketStatus::where([['id','=', $item['ticket_status_id']],['is_final','=', 1],['status_code','like','%RESOLVED%']])->findOrEmpty();
            // if(!$finalStatus->isEmpty()&&!$item['complete_time']){
            //     $canClose=true; 
            // }
            // $item['canClose'] = $canClose; 

            // 假设不可以催单; 
            // $canReminder = false; 

            //状态不是最终状态
            // $unfinalStatus = TicketStatus::where([['id','=', $item['ticket_status_id']],['is_final','<>',1]])->findOrEmpty();
            // if(!$unfinalStatus->isEmpty()){
            //    $canReminder = true; 
            // }
            // $item['canReminder'] = $canReminder; 

            // 假设不可以评价;
            // $canRate = false; 
            // $rateRecord = TicketServiceRatingLogic::detail(['ticket_id' => $item['id']]);//已有评价记录
            // if(!isset($rateRecord['id']) && $item['complete_time']){
            //     $canRate = true; // 允许评价
            // }
            // $item['canRate'] = $canRate; // 假设可以评价;
            // $item['rated'] = $rateRecord; // 评价记录;
        }
        unset($item); // 销毁引用变量
        // if($page == 1){
        //     $statusCounts = self::getTicketStuCounts($userId);
        // }else{
        $statusCounts = [];
        // }
        return [
            'statusCounts' => $statusCounts,
            'total' => $count,
            'lists' => $lists,
            'page' => $page,
            'pageSize' => $pageSize
        ];
    }

    /**
     * @notes 获取工单统计
     * @param int $userId
     * @param string $type 类型  user 我的工单  admin 技术员
     * @return array
     * <AUTHOR>
     */
    private static function getTicketStuCounts($userId, $type = 'user')
    {
        //获取当前租户的工单状态列表
        $statusList = TicketStatus::field('id,status_name as label,status_code as value,status_color as color')->select()->toArray();
        $totalCount = 0;
        if ($type == 'admin') {
            foreach ($statusList as $key => $value) {
                $where = [['dispatch_record.admin_id', '=', $userId],['dispatch_record.delete_time', '=', null], ['ticket.ticket_status_id', '=', $value['id']]];
                $sql = TicketModel::alias('ticket')
                    ->leftJoin('ticket_status status', 'ticket.ticket_status_id = status.id')
                    ->leftJoin('ticket_dispatch_record dispatch_record', 'ticket.id = dispatch_record.ticket_id')
                    ->field('ticket.*, status.status_name, status.status_color')
                    ->group('ticket.id')->where($where);
                $count = $sql->count();
                $statusList[$key]['count'] = $count;
                $totalCount += $count;
            }
        } else {
            foreach ($statusList as $key => $value) {
                $where = [['user_id', '=', $userId], ['ticket_status_id', '=', $value['id']]];
                $count = TicketModel::where($where)->count();
                $statusList[$key]['count'] = $count;
                $totalCount += $count;
            }
        }
        $allItem = [
            'id' => 0,
            'label' => '全部',
            'value' => 'all',
            'color' => '#333333',
            'count' => $totalCount
        ];
        array_unshift($statusList, $allItem);

        return $statusList;
    }

    /**
     * @notes 获取工单详情
     * @param array $params
     * @return array
     * <AUTHOR>
     * @date 2025/04/08 16:45
     */
    public static function detail($params)
    {
        $adminId = $params['admin_id'] ?? 0;
        if (isset($params['type']) && $params['type'] == 'staff' && !$adminId) {
            self::setError('非法访问');
            return [];
        }

        //工单动作
        if(isset($params['type'])&&$params['type'] == 'staff'){


            $where = [
                ['dispatch_record.admin_id', '=', $adminId], // 只查询处理人的工单
                ['dispatch_record.delete_time', '=', null], // 未删除的
                ['ticket.id', '=', $params['id']]
            ];
            
            $sql = TicketModel::alias('ticket')
            ->leftJoin('ticket_type type', 'ticket.ticket_type_id = type.id')
            ->leftJoin('ticket_status status', 'ticket.ticket_status_id = status.id')
            ->leftJoin('ticket_product product', 'ticket.product_id = product.id')
            ->leftJoin('ticket_company company', 'ticket.user_company_id = company.id')
            ->leftJoin('ticket_dispatch_record dispatch_record', 'ticket.id = dispatch_record.ticket_id')
            ->leftJoin('tenant_admin assignee', 'ticket.assignee_id = assignee.id')
            ->field('ticket.*, 
            type.type_name, 
            assignee.name as assignee_name,assignee.phone as assignee_phone,assignee.avatar as assignee_avatar,
            status.status_name, status.status_color, 
            product.name as product_name,product.code as product_code,product.brand as product_brand,product.label as product_label,product.model as product_model,product.maintenance_start_time as product_maintenance_start_time,product.maintenance_end_time as product_maintenance_end_time,
            company.company_name, company.company_address, company.company_latitude,company.company_longitude')
            ->where($where);

            // $query = $sql->fetchSql(true)->select();
            // var_dump($query);exit;
            $ticket = $sql->findOrEmpty();

            if ($ticket->isEmpty()) {
                self::setError('工单不存在');
                return [];
            }
            $detail = $ticket->toArray();
            // var_dump($detail);exit;
            //判断是参与或者是负责
            $detail['is_participate'] = 0; // 0 参与 1 负责
            if ($detail['assignee_id'] == $adminId) {
                $detail['is_participate'] = 1; // 1 负责
            }
            // 读取工单配置信息
            $ticketConfig = commonTicketService::getTicketConfig($detail['tenant_id']?? 0);
            $detail['actions'] = self::getStaffActionStatus($detail,$ticketConfig);
                // 获取处理人信息
            $detail['assignee_desensitize_name'] = desensitize_name($detail['assignee_name'] ?? '');
            $detail['assignee_desensitize_phone'] = desensitize_phone($detail['assignee_phone'] ?? '') ;
            //产品设备
            $detail['product_maintenance_status'] = TicketProductService::getMaintenanceStatus($detail['product_maintenance_start_time'], $detail['product_maintenance_end_time']);
            $tenantAdminModel = new TenantAdmin();
            $detail['assignee_avatar'] = $tenantAdminModel->getAvatarAttr($detail['assignee_avatar']);
            //相关工单
            if(!empty($detail['related_ticket_id'])){
                $detail['relatedTicket'] = TicketModel::where([['id','=',$detail['related_ticket_id']]])->findOrEmpty()->toArray();
            }else{
                $detail['relatedTicket'] = [];
            }
        }else{
            $ticket = TicketModel::with([
                'ticketType',
                'ticketStatus',
                'company',
                'product',
                'relatedTicket',
                'assignee' => function ($query) {
                    // 只选择需要返回的字段，可根据实际情况修改
                    $query->field('id, name, avatar, phone');
                }
            ])
            ->where([
                ['id', '=', $params['id']],
            ])
            ->findOrEmpty();
            if ($ticket->isEmpty()) {
                self::setError('工单不存在');
                return [];
            }
            $detail = $ticket->toArray();
            // 读取工单配置信息
            $ticketConfig = commonTicketService::getTicketConfig($detail['tenant_id']?? 0);
            $detail['actions'] = self::getTicketActionStatus($detail,$ticketConfig);

                    // 获取处理人信息
            if (!empty($detail['assignee'])) {
                $detail['assignee']['desensitize_name'] = desensitize_name($detail['assignee']['name'] ?? '');
                $detail['assignee']['desensitize_phone'] = desensitize_phone($detail['assignee']['phone'] ?? '');
            }
            //产品设备
            if (!empty($detail['product'])) {
                $detail['product']['maintenance_status'] = TicketProductService::getMaintenanceStatus($detail['product']['maintenance_start_time'], $detail['product']['maintenance_end_time']);
                // $detail['product']['label'] = $detail['product']['label']? explode('|', $detail['product']['label']) : []; 
            }
        }

        // 格式化图片和文件
        $images = !empty($detail['ticket_images']) ? json_decode($detail['ticket_images'], true) : [];
        if ($images) {
            $imagesArr = [];
            foreach ($images as &$image) {
                $imagesArr[] = FileService::getFileUrl($image);
            }
            $detail['ticket_images'] = $imagesArr;
        } else {
            $detail['ticket_images'] = [];
        }

        // 用户信息脱敏

        $detail['user_name_desensitize'] = desensitize_name($detail['user_name'])?? '';
        $detail['user_phone_desensitize'] = desensitize_phone($detail['user_phone'])?? ''; 
        $detail['user_email_desensitize'] = desensitize_email($detail['user_email'])?? ''; 

        // AI分析数据
        $detail['ai_analysis'] = json_decode($detail['ai_analysis'], true);

        // var_dump($detail);exit;

        return $detail;
    }

    /**
     * @notes 获取工单操作记录
     * @param array $params
     * @return array
     * hf_nsz
     */
    public static function actionTimeline($params)
    {
        $list = TicketEvent::alias('event')
            ->leftJoin('tenant_admin admin', 'admin.id = event.admin_id')
            ->leftJoin('tenant_admin_jobs admin_jobs', 'event.admin_id = admin_jobs.admin_id')
            ->leftJoin('tenant_jobs tenant_jobs', 'admin_jobs.jobs_id = tenant_jobs.id')
            ->leftJoin('user user', 'user.id = event.user_id')
            ->field([
                'event.*,
                admin.name as admin_name,
                admin.avatar as admin_avatar,
                admin.account as admin_account,
                MIN(admin_jobs.jobs_id) as jobs_id,
                MIN(tenant_jobs.name) as jobs_name,
                user.real_name as user_name,
                user.nickname as user_nickname,
                user.mobile as user_mobile,
                user.avatar as user_avatar,
                user.account as user_account'
            ])->where('event.ticket_id', $params['ticket_id'])
            ->group('event.id')
            ->order('event.id desc')
            ->select()->toArray();
        $userModel = new User();
        $tenantAdminModel = new TenantAdmin();
        foreach ($list as &$item) {
            $item['user_avatar'] = $userModel->getAvatarAttr($item['user_avatar']);
            $item['admin_avatar'] = $tenantAdminModel->getAvatarAttr($item['admin_avatar']);
        }
        return $list;
    }

    /**
     * @notes 获取工单回复记录
     * @param array $params
     * @return array
     * <AUTHOR>
     */
    public static function replyList($params)
    {
        // 获取工单回复记录
        $replies = TicketReply::alias('reply')->where('reply.ticket_id', $params['ticket_id'])
            ->leftJoin('user user', 'user.id = reply.replyer_id')
            ->leftJoin('tenant_admin admin', 'admin.id = reply.admin_id')
            ->leftJoin('tenant_admin_jobs admin_jobs', 'reply.admin_id = admin_jobs.admin_id')
            ->leftJoin('tenant_jobs tenant_jobs', 'admin_jobs.jobs_id = tenant_jobs.id')
            ->leftJoin('ticket ticket', 'ticket.id = reply.ticket_id')
            ->leftJoin('ticket_reply replyed', 'replyed.id = reply.reply_id')
            ->field([
                'reply.*',
                'user.real_name',
                'user.nickname',
                'user.mobile',
                'user.avatar',
                'user.account',
                'admin.name as admin_name',
                'admin.avatar as admin_avatar',
                'admin.account as admin_account',
                'admin_jobs.jobs_id',
                'tenant_jobs.name as jobs_name',
                'ticket.ticket_title',
                'replyed.reply_content as reply_reply_content '
            ])->order('reply.id desc')
            ->select()
            ->toArray();
        $userModel = new User();
        $tenantAdminModel = new TenantAdmin();
        foreach ($replies as &$reply) {
            // 格式化图片和文件
            $images = !empty($reply['reply_images']) ? json_decode($reply['reply_images'], true) : [];
            if ($images) {
                $imagesArr = [];
                foreach ($images as &$image) {
                    $imagesArr[] = FileService::getFileUrl($image);
                }
                $reply['reply_images'] = $imagesArr;
            } else {
                $reply['reply_images'] = [];
            }

            $reply['avatar'] = $userModel->getAvatarAttr($reply['avatar']);
            $reply['admin_avatar'] = $tenantAdminModel->getAvatarAttr($reply['admin_avatar']);

            // 确定回复人信息
            if ($reply['reply_type'] == 1) { // 用户回复
                // $reply['replier'] = $reply['user'] ?? [];
                $reply['replier_type'] = 'user';
            } else { // 管理员回复
                // $reply['replier'] = $reply['admin_user'] ?? [];
                $reply['replier_type'] = 'admin';
            }

            // unset($reply['user'], $reply['admin_user']);
        }

        return $replies;
    }

    /**
     * @notes 评价工单服务
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 16:50
     */
    public static function rateService($params)
    {
        $userId = $params['user_id'] ?? 0;

        $ticket = TicketModel::where([
            ['id', '=', $params['id']],
            ['user_id', '=', $userId]
        ])->findOrEmpty();

        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }

        if (!$ticket->complete_time) {
            self::setError('工单未完成，无法评价');
            return false;
        }
        //校验是否有评价记录
        if($ticket->rating_status == 1){
            self::setError('您已评价过该工单');
            return false;
        }
        // $rateRecord = TicketServiceRatingLogic::detail(['ticket_id' => $params['id']]); //已有评价记录
        // if (isset($rateRecord['id'])) {
        //     self::setError('您已评价过该工单');
        //     return false;
        // }

        Db::startTrans();
        try {
            //更新工单状态
            $ticket->rating_status = 1;
            $ticket->latest_processing_time = time();
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }
            //1.插入评价记录
            TicketServiceRatingLogic::add([
                'ticket_id' => $params['id'],
                'user_id' => $userId,
                'overall_satisfaction' => $params['overall_satisfaction'],
                'response_satisfaction' => $params['response_satisfaction'],
                'skill_satisfaction' => $params['skill_satisfaction'],
                'user_suggestion' => $params['user_suggestion'],
                //$params['label']  如果不存在则为空数组，如果存在，判段是否为数组或是字符串
                'label' => is_array($params['label']) ? $params['label'] : [$params['label']] ?? [],
            ]);

            //计算三个满意度分值平均分，取一位小数
            $overall_satisfaction = round($params['overall_satisfaction'], 1);
            $response_satisfaction = round($params['response_satisfaction'], 1);
            $skill_satisfaction = round($params['skill_satisfaction'], 1);
            $average_score = round(($overall_satisfaction + $response_satisfaction + $skill_satisfaction) / 3, 1);

            // 执行工单评价的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'customer_rating', 'api', ['rating_value' => $average_score, 'notice_type' => NoticeEnum::ORDER_SERVICE_RATING]);

            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::TICKET_SERVICE_RATING, [
                'admin_id' => $ticket->assignee_id, //工单处理人id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' => '用户ID：' . $ticket->user_id . '创建工单', //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 回复工单
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 16:55
     */
    public static function reply($params)
    {
        $whereTicket[] = ['id', '=', $params['id']];
        if ($params['reply_type'] == 2) {
            $adminId = $params['admin_id'] ?? 0;
            if (!$adminId) {
                self::setError('非法访问');
                return false;
            }
        } else {
            $userId = $params['user_id'] ?? 0;
            $whereTicket[] = ['user_id', '=', $userId];
        }

        $ticket = TicketModel::where($whereTicket)->findOrEmpty();

        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }

        if ($ticket->close_status > 0) { // 关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            self::setError('工单已关闭，无法回复');
            return false;
        }

        Db::startTrans();
        try {
            // 添加回复记录
            $reply = new TicketReply();
            $reply->tenant_id = request()->tenantId;
            $reply->ticket_id = $params['id'];
            if ($params['reply_type'] == 2) {
                $reply->admin_id = $adminId;
            } else {
                $reply->replyer_id = $userId;
            }
            $reply->reply_id = $params['reply_id'] > 0 ?? null;
            $reply->reply_type = $params['reply_type'];; // 用户回复
            $reply->reply_content = $params['reply_content'];
            $reply->reply_images = !empty($params['reply_images']) ? json_encode($params['reply_images']) : '';
            $reply->reply_time = time();
            $reply->save();

            // 执行工单留言的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'customer_complaint', 'api', ['notice_type' => NoticeEnum::ORDER_MESSAGE_COMMENT]);

            // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::REPLY_TICKET, [
                'admin_id' => $params['reply_type'] == 2 ? $adminId : 0, //工单处理人id
                'user_id' => $params['reply_type'] != 2 ? $userId : 0, //用户id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' => $params['reply_type'] == 2 ? '管理ID：' . $adminId . '留言评论' : '用户ID：' . $userId . '留言评论', //工单描述
                'reply_id' => $reply->id, //回复id
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
/**
     * @notes 完成
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     * 
     */
    public static function complete($params)
    {
        if (!$params['admin_id']) {
            self::setError('权限不足');
            return false;
        }
        //接单技术员ID
        $adminId = $params['admin_id'];
        $ticket = TicketModel::where([
            ['id', '=', $params['id']],
        ])->findOrEmpty();
        // 工单不存在
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        //判断工单是否为负责人
        if($ticket->assignee_id != $adminId){
            self::setError('工单不是负责人');
            return false;
        }
        //判断工单是否关闭
        if($ticket->close_status != 0 ){
            self::setError('工单已关闭');
            return false;
        }

        // 工单状态不是处理中状态
        $Status = TicketStatus::where([['id', '=', $ticket->ticket_status_id]])->findOrEmpty();
        if ($Status->isEmpty()) {
            self::setError('工单状态不存在');
            return false;
        }
        $old_status_id = $ticket->ticket_status_id;
        // 按排序sort,处理中状态的下一个指针状态
        $nextStatus = TicketStatus::where([['sort', '>', $Status->sort]])->order('sort asc')->findOrEmpty();
        if ($nextStatus->isEmpty()) {
            self::setError('没有下一个状态');
            return false;
        } else {
            // 下一个状态不是完成状态
            if ($nextStatus->is_final != 1) {
                self::setError('下一个状态不是完成状态');
                return false;
            } else {
                Db::startTrans();
                try {
                    //如果有处理方法processing_method，写入相应字段
                    if ($params['processing_method']) {
                        $ticket->processing_method = $params['processing_method'];
                    }
                    // 如果支付 确认已支付即可自动关闭工单
                    if ($ticket->payment_status == 2 && $ticket->client_confirmed_amount == 1 && $ticket->payment_amount > 0) {
                        $ticket->close_status = 1; // 客户关闭
                        $ticket->close_text = '管理ID：'.$adminId.'完成工单,客户已支付，自动关闭工单';
                    }
                    // 如果是免费工单
                    if($ticket->payment_status == 0){//免费工单
                        $ticket->close_status = 1; // 关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
                        $ticket->close_text = '管理ID：'.$adminId.'完成工单,工单为免费，自动关闭工单';
                    }

                    // 更新工单状态
                    $ticket->complete_time = time(); //完成时间 
                    $ticket->ticket_status_id = $nextStatus->id;
                    $ticket->latest_processing_time = time();

                    $result = $ticket->save();
                    //这里的result 返回的是 1 或者 0  1 成功  0 失败
                    if (!$result) {
                        throw new \Exception('工单更新失败');
                    }

                    // 更新工单状态 `ticket_status_time` 
                    $statusTime =  TicketStatusTimeLogic::executeTicketStatusTime($ticket->id, $old_status_id, $nextStatus->id, $adminId, $ticket->tenant_id,0);
                    if (!$statusTime) {
                        //抛出错误
                        throw new \Exception(TicketStatusTimeLogic::getError()??'更新状态节点失败');
                    }

                    // 执行工单状态变更的智能策略 完成状态没有改变
                    commonTicketService::executeSmartPolicy($ticket, 'status_change', 'api', ['previous_status_id' => $old_status_id,'notice_type' => NoticeEnum::ORDER_COMPLETE]);

                    // // 记录工单创建事件
                    commonTicketService::recordEvent($ticket,TicketEventEnum::MANAGER_RECEIVE_TICKET, [
                        'admin_id' => $adminId,//用户id
                        'ticket_status_id' => $ticket->ticket_status_id,//工单状态
                        'describe'=>  '管理ID：'.$adminId.'完成工单',//工单描述
                        'operation_ip' => request()->ip(),
                        'browser_type' => 1 //浏览器类型 1 手机 2 pc
                    ]);
                    Db::commit();
                    return true;
                } catch (\Exception $e) {
                    Db::rollback();
                    self::setError($e->getMessage());
                    return false;
                }
            }
        }
    }
    /**
     * @notes 关闭工单 终态
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */
    public static function close($params)
    {
        if (!isset($params['id']) || !isset($params['close_status']) || !isset($params['reason'])) {
            self::setError('缺少参数');
            return false;
        }
        $whereTicket = [
            ['id', '=', $params['id']],
        ];
        if ($params['close_status'] == 3) { //用户主动关闭
            $userId = $params['user_id'] ?? 0;
            $whereTicket[] = ['user_id', '=', $userId];
        } elseif ($params['close_status'] == 2) { //技术员关闭
            $adminId = $params['admin_id'] ?? 0;
            $whereTicket[] = ['assignee_id', '=', $adminId];
        } else { // 1 = 系统关闭
            self::setError('关闭状态错误');
            return false;
        }

        $ticket = TicketModel::where($whereTicket)->findOrEmpty();
        

        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        //判断工单是否关闭
        if ($ticket->close_status > 0) { // 关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            self::setError('工单已关闭');
            return false;
        }
        //待处理状态
        $Status = TicketStatus::where([['id', '=', $ticket->ticket_status_id]])->findOrEmpty();
        if ($Status->isEmpty()) {
            self::setError('工单状态不存在');
            return false;
        }

        if($Status->is_final == 0){//不是终态
            self::setError('工单状态不是关闭状态');
            return false;
        }
        $old_status_id = $ticket->ticket_status_id;

        Db::startTrans();
        try {
             // 更新工单状态 `ticket_status_time` 
             $statusTime =  TicketStatusTimeLogic::executeTicketStatusTime($ticket->id, $old_status_id, 0, $adminId, $ticket->tenant_id,$userId);
             if (!$statusTime) {
                 //抛出错误
                 throw new \Exception(TicketStatusTimeLogic::getError()??'更新状态节点失败');
             }
             //统计工单状态时间
             $ticketStatusTime = TicketStatusTimeLogic::calculateTotalTime($ticket->id,$ticket->tenant_id);
             // 更新工单状态
            $ticket->processing_total_duration = $ticketStatusTime; // 终态
            $ticket->close_status = $params['close_status']; //工单关闭状态
            $ticket->close_text = $params['reason'] ?? '用户主动关闭'; //工单关闭原因
            $ticket->latest_processing_time = time();
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }

            // 执行工单状态变更的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'status_change', 'api', ['previous_status_id' => $old_status_id, 'notice_type' => NoticeEnum::ORDER_CLOSE]);

            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::CLOSE_TICKET, [
                'user_id' => $params['close_status'] != 2 ? $userId : 0, //处理人id
                'admin_id' => $params['close_status'] == 2 ? $adminId : 0, //用户id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' =>  $params['reply_type'] == 2 ? '管理ID：' . $adminId . '关闭工单' : '用户ID：' . $userId . '关闭工单', //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
    /**
     * @notes 取消工单
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */
    public static function cancel($params)
    {
        if (!isset($params['id']) || !isset($params['close_status']) || !isset($params['reason'])) {
            self::setError('缺少参数');
            return false;
        }
        $userId = @$params['user_id']??0;
        $adminId = @$params['admin_id']??0;
        $whereTicket = [
            ['id', '=', $params['id']],
        ];
        if ($params['close_status'] == 3) { //用户主动关闭
            $whereTicket[] = ['user_id', '=', $userId];
        } elseif ($params['close_status'] == 2) { //技术员关闭
            $whereTicket[] = ['assignee_id', '=', $adminId];
        } else { // 1 = 系统关闭
            self::setError('关闭状态错误');
            return false;
        }

        $ticket = TicketModel::where($whereTicket)->findOrEmpty();

        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        //判断工单是否关闭
        if ($ticket->cancel_status != 0) { //  关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            self::setError('工单已取消');
            return false;
        }
        //判断工单是否关闭
        if ($ticket->close_status != 0) { //  关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            self::setError('工单已关闭，无法取消');
            return false;
        }
        //最终状态
        $finalStatus = TicketStatus::where([['id', '=', $ticket->ticket_status_id]])->findOrEmpty();
        if ($finalStatus->isEmpty()) {
            self::setError('工单最终状态不存在');
            return false;
        }
        if($finalStatus->is_final == 1){//不是终态
            self::setError('工单已是终态，不可取消');
            return false;
        }

        $old_status_id = $ticket->ticket_status_id;

        Db::startTrans();
        try {

            // 更新工单状态
            // $ticket->ticket_status_id = $finalStatus->id; //工单状态
            $ticket->cancel_status = $params['close_status']; //工单关闭状态
            $ticket->cancel_text = $params['reason'] ?? '用户主动取消'; //工单关闭原因
            $ticket->close_text = $params['reason'] ?? '用户主动取消'; //工单关闭原因
            $ticket->close_status = $params['close_status']; //工单关闭状态
            $ticket->latest_processing_time = time();
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }

            $statusTime = TicketStatusTimeLogic::executeTicketStatusTime($ticket->id,$old_status_id,0,$params['close_status']== 2?$adminId:0,$ticket->tenant_id,$params['close_status']== 2?0:$userId);
            if (!$statusTime) {
                //抛出错误
                throw new \Exception(TicketStatusTimeLogic::getError()??'更新状态节点失败');
            }

        // 执行工单取消的智能策略
           commonTicketService::executeSmartPolicy($ticket, 'order_cancel', 'api', ['previous_status_id' => $old_status_id, 'notice_type' => NoticeEnum::ORDER_CANCEL]);

            // // 记录工单创建事件

            commonTicketService::recordEvent($ticket, $params['close_status'] == 2 ? TicketEventEnum::MANAGER_CANCEL_TICKET : TicketEventEnum::CANCEL_TICKET, [
                'user_id' => $params['close_status'] != 2 ? $userId : 0, //处理人id
                'admin_id' => $params['close_status'] == 2 ? $adminId : 0, //用户id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' =>  $params['close_status'] == 2 ? '管理ID：' . $adminId . '取消工单' : '用户ID：' . $userId . '取消工单', //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);
            // 已支付的退款
            if ($ticket->payment_status == 3) {
                // 退款
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
    /**
     * @notes 变更工单紧急程度
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */
    public static function changeUrgent($params)
    {
        if (!isset($params['id']) || !isset($params['urgency'])) {
            self::setError('缺少参数');
            return false;
        }
        // 从紧急程度字典中判断是否存在
        $dict = DictData::where([['type_value', '=', 'urgency_degree'], ['value', '=', $params['urgency']]])->findOrEmpty();
        if ($dict->isEmpty()) {
            self::setError('紧急程度不存在');
            return false;
        }

        $whereTicket = [
            ['id', '=', $params['id']],
        ];
        if ($params['mode'] == 1) { //用户主动
            $userId = $params['user_id'] ?? 0;
            $whereTicket[] = ['user_id', '=', $userId];
        } elseif ($params['mode'] == 2) { //技术员
            $adminId = $params['admin_id'] ?? 0;
            if(isset($params['api_type'])&&$params['api_type'] == 'tenantapi'){

            }else{
                $whereTicket[] = ['assignee_id', '=', $adminId];
            }
        } else {
            self::setError('关闭状态错误');
            return false;
        }

        $ticket = TicketModel::where($whereTicket)->findOrEmpty();

        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }

        //判断工单是否关闭
        if ($ticket->close_status > 0) { //  关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            self::setError('工单已关闭，无法变更紧急程度');
            return false;
        }
        if($ticket->urgency == $params['urgency']){
            self::setError('紧急程度前后一致');
            return false;
        }
        $old_urgency = $ticket->urgency;

        Db::startTrans();
        try {

            // 更新工单状态
            $ticket->urgency = $params['urgency']; //紧急程度
            $ticket->latest_processing_time = time();
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }
            // 执行工单状态变更的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'urgency_change', 'api', ['previous_urgency' => $old_urgency, 'new_urgency' => $params['urgency'], 'new_urgency_name' => $dict['name'], 'notice_type' => NoticeEnum::ORDER_URGENT_CHANGE]);
            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::CHANGE_URGENCY_LEVEL, [
                'user_id' => $params['mode'] != 2 ? $userId : 0, //处理人id
                'admin_id' => $params['mode'] == 2 ? $adminId : 0, //用户id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' =>  $params['mode'] == 2 ? '管理ID：' . $adminId . '变更紧急程度' : '用户ID：' . $userId . '变更紧急程度', //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => $params['browser_type']??1 //浏览器类型 1 手机 2 pc
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 接单 已不使用
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/05/08 17:00
     */
    public static function accept($params)
    {
        //接单技术员ID
        $adminId = $params['admin_id'];
        $ticket = TicketModel::where([
            ['id', '=', $params['id']],
            // ['assignee_id', '=', $adminId],
            ['close_status', '=', 0] //未关闭
        ])->findOrEmpty();
        // 工单不存在
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }


        if($ticket->assignee_id || $ticket->close_status ){
            self::setError('工单状态异常');
            return false;
        }

        // 工单状态不是待处理状态
        $pendingStatus = TicketStatus::where([['id', '=', $ticket->ticket_status_id]])->findOrEmpty();
        if ($pendingStatus->isEmpty()) {
            self::setError('工单状态不是待处理状态');
            return false;
        }
        $old_status_id = $ticket->ticket_status_id;
        // 按排序sort,待处理状态的下一个指针状态
        $nextStatus = TicketStatus::where([['sort', '>', $pendingStatus->sort]])->order('sort asc')->findOrEmpty();
        if ($nextStatus->isEmpty()) {
            self::setError('没有下一个状态');
            return false;
        }

        Db::startTrans();
        try {
            if (!$ticket->first_receive_time) {
                $ticket->first_receive_time = time(); //首次接单时间
            }
            if (!$ticket->receiver_admin_id) {
                $ticket->receiver_admin_id = $adminId; //接单技术员ID
            }
            // 更新工单状态
            $ticket->ticket_status_id = $nextStatus->id; //工单状态
            $ticket->latest_processing_time = time();
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }

            $statusTime = TicketStatusTimeLogic::executeTicketStatusTime($ticket->id,$old_status_id,$nextStatus->id,$adminId,$ticket->tenant_id);
            if (!$statusTime) {
                //抛出错误
                throw new \Exception(TicketStatusTimeLogic::getError()??'更新状态节点失败');
            }

            // 执行工单状态变更的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'status_change', 'api', ['previous_status_id' => $old_status_id, 'notice_type' => NoticeEnum::ORDER_ACCEPT]);

            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::MANAGER_RECEIVE_TICKET, [
                'admin_id' => $adminId, //用户id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' =>  '管理ID：' . $adminId . '接单处理', //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**设置支付金额
     * @notes 设置支付金额
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     * */

    public static function setPayAmount($params){
        if(!$params['admin_id']){
            self::setError('权限不足');
            return false;
        }
        //技术员ID
        $adminId = $params['admin_id'];
        $ticket = TicketModel::where([
            ['id', '=', $params['id']],
        ])->findOrEmpty();
        // 工单不存在
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        // 判断是否为负责人
        if ($ticket->assignee_id != $adminId) {
            self::setError('您不是工单负责人');
            return false;
        }
        //判断是否已关闭
        if ($ticket->close_status!= 0) {
            self::setError('工单已关闭');
            return false;
        }
        // 工单不是免费状态
        if ($ticket->payment_status != 0) {
            self::setError('当前不能设置金额');
            return false;
        }
        //修改工单为支付工单payment_status = 1 并设置支付金额payment_amount和用户待确认client_confirmed_amount=0
        Db::startTrans();
        try {
            //更新工单状态
            $ticket->payment_status = 1; //变更为已支付
            $ticket->payment_amount = $params['pay_amount']; //支付金额
            $ticket->client_confirmed_amount = 0; //用户待确认
            $ticket->latest_processing_time = time();
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }
            // 执行设置工单付费状态的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'amount_change', 'api', ['amount' => $params['pay_amount'], 'notice_type' => NoticeEnum::ORDER_AMOUNT_CHANGE]);

            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::CHANGE_TICKET_AMOUNT, [
                'admin_id' => $adminId, //用户id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' =>  '管理ID：' . $adminId . '设置金额为：' . $params['pay_amount'], //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());

            return false;
        }
    }

    /**
     * @notes 提醒用户确认费用
     * @param array $params
     * @return bool 
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */
    public static function notifyConfirmAmount($params){
        if(!$params['admin_id']){
            self::setError('权限不足');
            return false;
        }

        //技术员ID
        $adminId = $params['admin_id'];
        $ticket = TicketModel::where([
            ['id', '=', $params['id']],
        ])->findOrEmpty();
        // 工单不存在
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }

        // 工单不是负责人
        if ($ticket->assignee_id!= $adminId) {
            self::setError('您不是工单负责人');
            return false;
        }
        // 工单不是待支付
        if ($ticket->payment_status!= 1) {
            self::setError('工单状态不是待支付');
            return false;
        }
        // 工单用户未确认
        if ($ticket->client_confirmed_amount != 0) {
            self::setError('用户已确认');
            return false;
        }
        // 工单未关闭
        if ($ticket->close_status!= 0) {
            self::setError('工单已关闭');
            return false;
        }

        // 设置缓存cache 每日可提醒一次 
        $cacheKey = 'notify_confirm_amount_' . date('Ymd') . '_ticket' . $ticket->id . '_admin' . $adminId;
        $cache = cache($cacheKey);
        if ($cache) {
            self::setError('您今天已提醒');
            return false;
        }
        try {
            // 记录提醒确认费用缓存，设置缓存有效期到当天 23:59:59
            $expireTime = strtotime('tomorrow') - time();
            cache($cacheKey, true, $expireTime);
            // 执行提醒用户确认金额的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'notify_confirm_amount', 'api', ['notice_type' => NoticeEnum::ORDER_URGENT_CONFIRM_AMOUNT]);

            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::REMIND_USER_CONFIRM_AMOUNT, [
                'admin_id' => $adminId, //用户id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' =>  '管理ID：' . $adminId . '提醒客户确认金额：' . $ticket->payment_amount, //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);
            return true;
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
    /**
     * @notes 确认支付
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */

    public static function confirmPay($params){
        if(!$params['admin_id']){
            self::setError('权限不足');
            return false;
        }
        //技术员ID
        $adminId = $params['admin_id'];
        $ticket = TicketModel::where([
            ['id', '=', $params['id']],
        ])->findOrEmpty();
        // 工单不存在
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        // 工单不存在
        if ($ticket->assignee_id != $adminId) {
            self::setError('您不是工单负责人');
            return false;
        }
        // 工单不存在
        if ($ticket->payment_status!= 1) {
            self::setError('工单状态不是待支付');
            return false;
        }

        $status = TicketStatus::where([['id', '=', $ticket->ticket_status_id]])->findOrEmpty();
        if ($status->isEmpty()) {
            self::setError('工单状态不存在');
            return false;
        }

        //修改工单为支付工单payment_status = 2 并用户待确认client_confirmed_amount=1
        Db::startTrans();
        try {
            //更新工单状态
            $ticket->payment_status = 2;//变更为已确认
            $ticket->client_confirmed_amount = 1;//用户已确认

            // 工单已是最后一个状态 支付即可关闭
            if($status->is_final == 1){
                $ticket->close_status = 1;//变更为已关闭
                if(!$ticket->complete_time){//如果存在完成时间
                    $ticket->complete_time = time();
                }
                $ticket->close_text = '管理ID：'.$adminId.'确认客户支付金额：'.$ticket->payment_amount.',工单已是终态statusID:'.$ticket->ticket_status_id.'自动关闭工单';//关闭时间
            }

            $ticket->latest_processing_time = time();
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }
            // 执行设置工单付费状态的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'comfirm_pay', 'api', ['amount' => $ticket->payment_amount, 'notice_type' => NoticeEnum::ORDER_PAY]);
            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::TICKET_PAYMENT, [
                'admin_id' => $adminId, //用户id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' =>  '管理ID：' . $adminId . '确认客户支付金额：' . $ticket->payment_amount, //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 提醒用户支付费用
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00

    */
    public static function notifyPay($params){
        if(!$params['admin_id']){
            self::setError('权限不足');
            return false;
        }
        //技术员ID
        $adminId = $params['admin_id'];
        $ticket = TicketModel::where([
            ['id', '=', $params['id']],
        ])->findOrEmpty();
        // 工单不存在
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        //判断是不是负责人
        if ($ticket->assignee_id!= $adminId) {
            self::setError('您不是工单负责人');
            return false;
        }
        // 判断工单是否是待支付
        if ($ticket->payment_status!= 1) {
            self::setError('工单状态不是待支付');
            return false;
        }
        //判断工单是否关闭
        if ($ticket->close_status!= 0) {
            self::setError('工单已关闭');
            return false;
        }
        //
        // 设置缓存cache 每日可提醒一次
        $cacheKey = 'notify_pay_' . date('Ymd') . '_ticket' . $ticket->id . '_admin' . $adminId;
        $cache = cache($cacheKey);
        if ($cache) {
            self::setError('您今天已提醒');
            return false;
        }
        try {
            // 记录提醒支付缓存，设置缓存有效期到当天 23:59:59
            $expireTime = strtotime('tomorrow') - time();
            cache($cacheKey, true, $expireTime);
            // 执行提醒用户确认金额的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'notify_pay', 'api', ['notice_type' => NoticeEnum::ORDER_URGENT_PAY]);
            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::REMIND_USER_PAYMENT, [
                'admin_id' => $adminId, //用户id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' =>  '管理ID：' . $adminId . '提醒客户支付金额：' . $ticket->payment_amount, //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);
            return true;
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
    /**
     * @notes 设置免费
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */

    public static function setFree($params){
        if(!$params['admin_id']){
            self::setError('权限不足');
            return false;
        }
        //技术员ID
        $adminId = $params['admin_id'];
        $ticket = TicketModel::where([
            ['id', '=', $params['id']],
        ])->findOrEmpty();
        // 工单不存在
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        // 判断是否为负责人
        if ($ticket->assignee_id!= $adminId) {
            self::setError('您不是工单负责人');
            return false;
        }
        // 判断工单是否关闭
        if ($ticket->close_status!= 0) {
            self::setError('工单已关闭');
            return false;
        }
        // 工单不是支付状态
        if ($ticket->payment_status == 0) {
            self::setError('当前不能设置免费');
            return false;
        }
        // 工单已是最后一个状态 支付即可关闭
        $status = TicketStatus::where([['id', '=', $ticket->ticket_status_id]])->findOrEmpty();
        if ($status->isEmpty()) {
            self::setError('工单状态不存在');
            return false;
        }

        //修改工单为免费状态payment_status = 0 并设置支付金额payment_amount=0 用户待确认client_confirmed_amount=0
        Db::startTrans();
        try {
            //更新工单状态
            $ticket->payment_status = 0; //变更为免费
            $ticket->payment_amount = 0; //支付金额
            $ticket->client_confirmed_amount = 0; //用户待确认

            // 工单已是最后一个状态 支付即可关闭
            if($status->is_final == 1){
                $ticket->close_status = 1;//变更为已关闭
                $ticket->complete_time = time();
            }

            $ticket->latest_processing_time = time();
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }
            // 执行设置工单免费状态的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'set_free', 'api', ['amount' => 0, 'notice_type' => NoticeEnum::ORDER_AMOUNT_CHANGE]);
            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::CHANGE_TICKET_AMOUNT, [
                'admin_id' => $adminId, //用户id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' =>  '管理ID：' . $adminId . '将工单设置为免费', //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
    /**
     * @notes 修改工单金额
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */

    public static function updateAmount($params){
        if(!$params['admin_id']){
            self::setError('权限不足');
            return false;
        }
        //技术员ID
        $adminId = $params['admin_id'];
        $ticket = TicketModel::where([
            ['id', '=', $params['id']]
        ])->findOrEmpty();
        // 工单不存在
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        // 判断是否为负责人
        if ($ticket->assignee_id!= $adminId) {
            self::setError('您不是工单负责人');
            return false;
        }
        // 判断工单是否关闭
        if ($ticket->close_status!= 0) {
            self::setError('工单已关闭');
            return false;
        }
        // 工单不是支付状态
        if ($ticket->payment_status == 0) {
            self::setError('当前不能修改金额');
            return false;
        }
        //修改工单金额payment_amount  用户待确认client_confirmed_amount 支付状态payment_status
        Db::startTrans();
        try {
            //更新工单状态
            $ticket->payment_amount = $params['pay_amount']; //支付金额
            $ticket->client_confirmed_amount = 0; //用户待确认 
            $ticket->payment_status = 1;
            $ticket->latest_processing_time = time();
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }
            // 执行修改工单金额的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'amount_change', 'api', ['amount' => $params['pay_amount'], 'notice_type' => NoticeEnum::ORDER_AMOUNT_CHANGE]);
            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::CHANGE_TICKET_AMOUNT, [
                'admin_id' => $adminId, //用户id
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' =>  '管理ID：' . $adminId . '将工单金额改为：' . $params['pay_amount'], //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 确认工单费用
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */
    public static function confirmAmount($params)
    {
        $userId = $params['user_id'];

        $ticket = TicketModel::where([
            ['id', '=', $params['id']],
        ])->findOrEmpty();

        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        if($ticket->user_id != $userId){
            self::setError('不是您提交的工单');
            return false;
        }
        // 不是待分配状态 或 不是待处理状态
        if ($ticket->payment_status != 1 &&  $ticket->client_confirmed_amount != 0) {
            self::setError('不是待支付、待确认状态');
            return false;
        }

        Db::startTrans();
        try {

            // 更新工单状态
            $ticket->client_confirmed_amount = 1; //变更为已确认
            $ticket->latest_processing_time = time();
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }

            // 执行工单确认金额的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'confirm_mount','api',['amount' => $ticket['payment_amount'],'notice_type' => NoticeEnum::ORDER_CONFIRM_AMOUNT]);
             // // 记录工单创建事件
             commonTicketService::recordEvent($ticket,TicketEventEnum::CONFIRM_TICKET_AMOUNT, [
                'user_id' => $userId,//用户id
                'ticket_status_id' => $ticket->ticket_status_id,//工单状态
                'describe'=>  '用户ID：'.$userId.'确认金额改为：'.$ticket->payment_amount,//工单描述

                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
    /**
     * @notes 催单
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */
    public static function reminder($params)
    {
        $userId = $params['user_id'];

        $ticket = TicketModel::where([
            ['id', '=', $params['id']]
        ])->findOrEmpty();

        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        if($ticket->user_id!= $userId){
            self::setError('不是您提交的工单');
            return false;
        }

        if($ticket->is_timeout == 0){//超时
            self::setError('工单按正常进度进行中，耐心等待吧');
            return false;
        }
        //状态不是最终状态
        $unfinalStatus = TicketStatus::where([['id', '=', $ticket->ticket_status_id], ['is_final', '<>', 1]])->findOrEmpty();
        // 终态不可以催单
        if ($unfinalStatus->isEmpty()) {
            self::setError('订单状态不可催单');
            return false;
        }
        //通过缓存记录用户每个订单当只能催单一次
        $cacheKey = 'ticket_reminder_' . date('Ymd') . '_user' . $userId . '_ticket' . $ticket['id'];
        if (cache($cacheKey)) {
            self::setError('今天已催单');
            return false; // 如果缓存存在，不允许催单
        }
        Db::startTrans();
        try {
            // 这里原代码更新工单状态和确认金额逻辑可能有误，推测是催单逻辑不需要此操作，可根据实际情况调整
            // 记录催单缓存，设置缓存有效期到当天 23:59:59
            $expireTime = strtotime('tomorrow') - time();
            cache($cacheKey, true, $expireTime);

            // 执行催单的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'reminder_order', 'api', ['notice_type' => NoticeEnum::ORDER_REMIND]);
            //写入事件表
            $eventModel = new TicketEvent();
            $ticketEvent_data = [
                'tenant_id'    => $ticket->tenant_id,
                'ticket_id'    => $ticket->id,
                'ticket_status_id' => $ticket->ticket_status_id,
                'admin_id'     => $ticket->assignee_id, // 处理人,
                'event_type'   => TicketEventEnum::USER_REMIND, // 用户催单
                'browser_type' => 1,    // 浏览器标识，5=系统自动修改
                'operation_ip' => request()->ip() ?? '',    // 操作IP
                'create_time'  => time(),
                'update_time'  => time(),
            ];
            $eventModel->save($ticketEvent_data);

            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::USER_REMIND, [
                'user_id' => $userId, //用户id
                'admin_id' => $ticket->assignee_id, //处理人
                'ticket_status_id' => $ticket->ticket_status_id, //工单状态
                'describe' =>  '用户ID：' . $userId . '催管理ID' . $ticket->assignee_id . '尽快处理工单', //工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**

     * @notes 工单改派技术员
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */

    public static function changeToDispatch($params){
        if(!$params['admin_id']){
            self::setError('没有权限');
            return false;
        }
        //技术员ID
        $adminId = $params['admin_id'];
        $where = [
            ['id', '=', $params['id']],

        ];


        $ticket = TicketModel::where($where)->findOrEmpty();
        // 工单不存在
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }

        if($ticket->close_status != 0){
            self::setError('工单已关闭');
            return false;
        }
        
        //@nb 非租户端修改时处理人时，只能修改派给自己的工单
        if (!isset($params['api_type']) ||  $params['api_type'] !== 'tenantapi') {
            if($ticket->assignee_id != $adminId){
                self::setError('您不是负责人');
                return false;
            }
        }else{

        }

        if($ticket->assignee_id == $params['staff_id']){
            self::setError('不能改派给自己');
            return false;
        }
        // 有指派的技术员ID 
        if ($params['staff_id'] && !$params['dept_id']) {
            // 获取指派技术员所属部门ID
            $staff = TenantAdminDept::where('admin_id', $params['staff_id'])->findOrEmpty();
            if ($staff->isEmpty()) {
                $params['dept_id'] = 0;
            } else {
                $params['dept_id'] = $staff->dept_id;
            }
        }
        $previous_assignee_id = $ticket->assignee_id; // 记录变更前的处理人ID

        // 工单状态不是待处理
        $status = TicketStatus::where([['id', '=', $ticket->ticket_status_id]])->findOrEmpty();
        if ($status->isEmpty()) {
            self::setError('工单状态不存在');
            return false;
        }
        if ($status->is_initial != 1) {
            if(!$params['staff_id']){//不是释放到大厅 指定给某管理技术
                self::setError('当前状态不能释放到抢单大厅');
                return false;
            }
        }

        $now = time(); // 当前时间
        // 变更工单处理人 新增派单记录 执行智能策略
        Db::startTrans();
        try {
            //更新工单状态
            $ticket->assignee_id = $params['staff_id'];//变更为
            $ticket->dept_id = $params['dept_id'];//变更为
            if(!$params['staff_id']){
                $ticket->allocation_status = 0;//派单状态变更为0
                $ticket->allocation_ticket_time = 0;//派单时间变更为0
            }else{
                $ticket->allocation_status = 1; // 已分配
                $ticket->allocate_ticket_time = $now; //分配时间当前时间
                if(is_null($ticket->first_receive_time )){ //首次接单时间：有没有值，有就不管，没有就填当前时间
                    $ticket->first_receive_time = $now;
                }
            }

            $ticket->latest_processing_time = $now;
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }

            if($params['staff_id']){//不是释放到大厅 指定给某管理技术
                // 新增派单记录
                // 更新分配记录 `ticket_dispatch_record` 新增接单人记录
                $ticketDispatchRecord_data = [
                    'tenant_id'                => $ticket->tenant_id,               // 租户ID
                    'ticket_id'                => $ticket->id,                        // 工单ID
                    'dept_id'                  => $params['dept_id'] ,                          // 处理人部门ID
                    'admin_id'                 => $params['staff_id'],                          // 处理人ID
                    'processing_time'          => $now,                             // 处理时间
                    'processing_content'       => '改派',                            // 处理内容
                    'processing_status_id'     => $ticket->ticket_status_id,        // 处理状态ID
                    'processing_description'   => '管理ID：'.$adminId.'将工单转派给管理ID'.$params['staff_id'],                            // 处理说明
                    'dispatch_time'            => $now,                             // 派单时间
                    'dispatcher_id'            => $adminId,                          // 派单人ID
                    'create_time'              => $now,                             // 创建时间
                    'update_time'              => $now,                             // 更新时间
                ];
                $ticketDispatchRecord =  new TicketDispatchRecord();
                $res_addDispatchRecord = $ticketDispatchRecord->save($ticketDispatchRecord_data);
                if ($res_addDispatchRecord === false) {
                    throw new \Exception('新增派单记录异常，转单失败！');
                }
            }
            // 执行工单变更处理人的智能策略 没有状态改变
            // commonTicketService::executeSmartPolicy($ticket, 'status_change', 'api', ['previous_assignee_id' => $previous_assignee_id, 'new_assignee_id' => $params['staff_id'], 'notice_type' => NoticeEnum::ORDER_CHANGE]);
            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket,TicketEventEnum::CHANGE_HANDLER, [
                'admin_id' => $adminId,//处理人
                'ticket_status_id' => $ticket->ticket_status_id,//工单状态
                'describe'=>  $params['staff_id']?'管理ID：'.$adminId.'将工单转派给管理ID'.$params['staff_id']:'管理ID：'.$adminId.'将工单释放到抢单大厅',//工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => $params['browser_type'] ?? 1, //浏览器标识，1=手机，2=PC，3=小程序，4=后台修改，5=系统自动修改
            ]);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**

     * @notes 工单改派参与人（协同处理人）
     * @param array $params
     * @return bool
     * <AUTHOR> copy by $this->changeToDispatch($params)
     * @date 2025/05/23
     */
    public static function changeParticipants($params)
    {
        //处理人ID
        $adminId = $params['admin_id'];
        $where = [
            ['id', '=', $params['id']],
            ['close_status', '=', 0] //未关闭
        ];

        //@nb 非租户端修改时处理人时，只能修改派给自己的工单
        if (!isset($params['api_type']) ||  $params['api_type'] !== 'tenantapi') {
            $where[] = ['assignee_id', '=', $adminId];
        }

        $ticket = TicketModel::where($where)->findOrEmpty();
        // 工单不存在
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }

        // 有指派的处理人ID 
        if ($params['staff_id'] && !$params['dept_id']) {
            // 获取指派技术员所属部门ID
            $staff = TenantAdminDept::where('admin_id', $params['staff_id'])->findOrEmpty();
            if ($staff->isEmpty()) {
                $params['dept_id'] = 0;
            } else {
                $params['dept_id'] = $staff->dept_id;
            }
        }

        // 变更工单处理人 新增派单记录 执行智能策略
        $now = time();
        Db::startTrans();
        try {
            //更新工单状态
            $ticket->latest_processing_time = $now;
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }

            if ($params['staff_id']) {
                $params_ext = [
                    'dept_id' => $params['dept_id'],
                    'dispatcher_id' => $adminId,
                    'dispatch_time' => $now
                ];
                $params_r = [
                    'id' => $params['id'],
                    'dispatch_admin_ids' => $params['staff_ids'],
                ];
                // 处理参与人（协同处理人）
                CommonTicketService::handleRelation($params_r, 'dispatch_admin_ids', TicketDispatchRecord::class, $params_ext);
            }

            // 记录工单创建事件
            commonTicketService::recordEvent($ticket, TicketEventEnum::CHANGE_PARTICIPANTS, [
                'admin_id' => $adminId, //处理人
                'ticket_status_id' => $ticket->ticket_status_id, // 工单状态
                'describe' =>  '管理ID：' . $adminId . '修改参与人为ID：' . $params['staff_ids'], // 工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => $params['browser_type'] ?? 1, //浏览器类型 1 手机 2 pc
            ]);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /* @notes 抢单
     * @param array $params
     * @return array
     * <AUTHOR>
     * @date 2025/05/06
     */
    public static function grab($params)
    {
        $now = time(); // 当前时间
        $ticketID = $params['id']; // 工单ID

        // 检查用户
        $adminId = $params['admin_id'];
        $admin = TenantAdmin::findOrEmpty($adminId);
        if ($admin->isEmpty()) {
            self::setError('当前账号状态异常，抢单失败！');
            return false;
        }

        // $deptID = User::getDeptID($userId); // 用户部门ID
        $deptIDArr = (new TenantAdmin)->getDeptIdAttr(true, ['id' => $adminId]); // 用户部门ID
        if (empty($deptIDArr)) {
            $deptID = 0; // 用户部门ID
        } else {
            $deptID = $deptIDArr[0]; // 用户部门ID
        }

        // 检查工单
        $ticket = TicketModel::where([['id', '=', $ticketID]])->findOrEmpty();
        if ($ticket->isEmpty()) {
            self::setError('工单异常');
            return false;
        }
        //检查工单是否关闭
        if($ticket->close_status!= 0){
            self::setError('工单已关闭');
            return false;
        }
        // 检查工单是否已分配
        if ($ticket->assignee_id == $adminId) {
            self::setError('您已是工单处理人，无需重复抢单！');
            return false;
        }

        $nowStatusID = $ticket->ticket_status_id; // 工单当前状态ID;
        $nextStatusID = $ticket->ticket_status_id; // 工单下一个状态ID，默认为当前
        $ticketStatus = TicketStatus::where([['id', '=', $ticket->ticket_status_id]])->findOrEmpty(); // 工单当前状态
        if ($ticketStatus->isEmpty()) {
            self::setError('工单状态异常');
            return false;
        }
        // 不是终态，更新成下一个状态
        if ($ticketStatus->is_final === 0) {
            $nextStatus = TicketStatus::where([['sort', '>', $ticketStatus->sort]])->order('sort asc')->findOrEmpty();
            
            if (!$nextStatus->isEmpty()) $nextStatusID = $nextStatus->id;
        }

        Db::startTrans();
        try {
            // 更新工单表 `ticket`
            $ticket->allocation_status = 1; // 已分配
            $ticket->ticket_status_id =  $nextStatusID; // 当前状态的下一个状态 状态应该不变
            $ticket->allocate_ticket_time = $now; //分配时间当前时间
            if(is_null($ticket->first_receive_time )){ //首次接单时间：有没有值，有就不管，没有就填当前时间
                $ticket->first_receive_time = $now;
            }
            $ticket->assignee_id = $adminId;  // 处理人ID = 抢单人的ID

            // $ticket->client_confirmed_amount = 1; // 客户确认金额，0=否，1=是  hf_nsz这里不应该改动
            $ticket->latest_processing_time = $now; // 最新处理时间
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }

            // 更新分配记录表 `ticket_dispatch_record` 新增接单人记录
            $ticketDispatchRecord_data = [
                'tenant_id'                => $ticket->tenant_id,               // 租户ID
                'ticket_id'                => $ticketID,                        // 工单ID
                'dept_id'                  => $deptID,                          // 处理人部门ID
                'admin_id'                 => $adminId,                          // 处理人ID
                'processing_time'          => $now,                             // 处理时间
                'processing_content'       => '抢单',                            // 处理内容
                'processing_status_id'     => $ticket->ticket_status_id,        // 处理状态ID
                'processing_description'   => '抢单',                            // 处理说明
                'dispatch_time'            => $now,                             // 派单时间
                'dispatcher_id'            => $adminId,                          // 派单人ID
                'create_time'              => $now,                             // 创建时间
                'update_time'              => $now,                             // 更新时间
            ];
            $ticketDispatchRecord =  new TicketDispatchRecord();
            $res_addDispatchRecord = $ticketDispatchRecord->save($ticketDispatchRecord_data);
            if ($res_addDispatchRecord === false) {
                self::setError('新增派单记录异常，抢单失败！');
                Db::rollback();
                return false;
            }
            // 工单当前状态时间
            $statusTime = TicketStatusTimeLogic::executeTicketStatusTime($ticket->id,$nowStatusID,$nextStatusID,$adminId,$ticket->tenant_id,0);
            if (!$statusTime) {
                //抛出错误
                throw new \Exception(TicketStatusTimeLogic::getError()??'更新状态节点失败');
            }

            // 更新工单状态表 `ticket_status_time` 改当前状态的结束时间，并且插入一条新状态的记录


            // 状态都没变，就不更新
            // $ticketStatusTime = TicketStatusTime::where([
            //     ['ticket_id', '=', $ticketID],
            //     ['status_id', '=', $nowStatusID],
            // ])->findOrEmpty();

            // if ($ticketStatusTime->isEmpty()) { // 工单当前状态时间不存在，新增一条
            //     $ticketStatusTime_data = [
            //         'tenant_id'    => $ticket->tenant_id,
            //         'ticket_id'    => $ticketID,
            //         'previous_status_id' => $nowStatusID, // 上一个状态ID
            //         'status_id'    => $nextStatusID,
            //         'start_time'   => $now,
            //         'operator_admin_id' => $adminId,
            //         'create_time'  => $now,
            //         'update_time'  => $now,
            //     ];
            //     $ticketStatusTime = new TicketStatusTime();
            //     $res_addStatusTime = $ticketStatusTime->save($ticketStatusTime_data);
            //     if ($res_addStatusTime === false) {
            //         self::setError('新增工单当前状态时间异常，抢单失败！');
            //         Db::rollback();
            //         return false;
            //     }
            // } else { // 工单当前状态时间存在，更新结束时间
            //     $ticketStatusTime->end_time = $now;
            //     $ticketStatusTime->update_time = $now;
            //     $res_updateStatusTime = $ticketStatusTime->save();
            //     if ($res_updateStatusTime === false) {
            //         self::setError('更新工单当前状态时间异常，抢单失败！');
            //         Db::rollback();
            //         return false;
            //     }
            // }

            // 更新工单事件 `ticket_event` event_type = 9
            $ticketEvent_data = [
                'tenant_id'    => $ticket->tenant_id,
                'ticket_id'    => $ticketID,
                'admin_id'     => $adminId,
                'ticket_status_id' => $ticket->ticket_status_id,
                'event_type'   => TicketEventEnum::CHANGE_HANDLER, // 事件类型，9=更改处理人
                'describe'     => '管理员'.$adminId.'抢单',
                'browser_type' => 5, // 浏览器标识，5=系统自动修改
                'create_time'  => $now,
                'update_time'  => $now,
            ];
            $ticketEvent = new TicketEvent();
            $res_updateEvent = $ticketEvent->save($ticketEvent_data);
            if ($res_updateEvent === false) {
                self::setError('更新工单事件异常，抢单失败！');
                Db::rollback();
                return false;
            }

            // 工单当前事件 记录事件应该新增不是修改已存在

            // $ticketEvent = TicketEvent::where([
            //     ['ticket_id', '=', $ticketID],
            //     ['ticket_status_id', '=', $nowStatusID],
            // ])->findOrEmpty();
            // if ($ticketEvent->isEmpty()) { // 工单当前事件不存在，新增一条
            //     $ticketEvent_data = [
            //         'tenant_id'    => $ticket->tenant_id,
            //         'ticket_id'    => $ticketID,
            //         'admin_id'     => $adminId,
            //         'ticket_status_id' => $nextStatusID,
            //         'event_type'   => TicketEventEnum::CHANGE_HANDLER, // 事件类型，9=更改处理人
            //         'browser_type' => 5, // 浏览器标识，5=系统自动修改
            //         'create_time'  => $now,
            //         'update_time'  => $now,
            //     ];
            //     $ticketEvent = new TicketEvent();
            //     $res_updateEvent = $ticketEvent->save($ticketEvent_data);
            //     if ($res_updateEvent === false) {
            //         self::setError('更新工单事件异常，抢单失败！');
            //         Db::rollback();
            //         return false;
            //     }

            // }else{ // 工单当前事件存在，更新
            //     $ticketEvent->event_type = TicketEventEnum::CHANGE_HANDLER;  // 事件类型，9=更改处理人
            //     $ticketEvent->update_time = $now;
            //     $res_addEvent = $ticketEvent->save();
            //     if ($res_addEvent === false) {
            //         self::setError('新增工单当前事件异常，抢单失败！');
            //         Db::rollback();
            //         return false;
            //     }
            // }

            // 执行工单评价的智能策略 常量TRIGGER_STATUS_CHANGE
            commonTicketService::executeSmartPolicy($ticket, 'status_change', 'api', ['previous_status_id' => $nowStatusID, 'notice_type' => NoticeEnum::ORDER_GRAB]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
        /**
     * @notes 退出参与
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */
    public static function exitParticipate($params)
    {
        if (!$params['id'] || !$params['admin_id']) {
            self::setError('缺少参数');
            return false;
        }
        $adminId = $params['admin_id'];
        // 检查是否存在分配记录表
        $ticketDispatchRecord = TicketDispatchRecord::where([['ticket_id', '=', $params['id']],['admin_id','=',$params['admin_id']]])->findOrEmpty();
        if ($ticketDispatchRecord->isEmpty()) {
            self::setError('您不是参与人');
            return false;
        }

        $ticket = TicketModel::where('id',$params['id'])->findOrEmpty();

        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        //判断工单是否关闭
        if ($ticket->close_status > 0) { //  关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            self::setError('工单已关闭');
            return false;
        }

        Db::startTrans();
        try {
            // 更新工单状态
            $ticketDispatchRecord->delete_time = time(); //删除时间
            $ticketDispatchRecord->save();
            // // 记录工单创建事件
            commonTicketService::recordEvent($ticket,TicketEventEnum::EXIT_PARTICIPATION, [
                'user_id' => 0,//处理人id
                'admin_id' => $adminId,//用户id
                'ticket_status_id' => $ticket->ticket_status_id,//工单状态
                'describe'=>  '管理ID：'.$adminId.'退出参与该工单',//工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
    /**
     * 变更状态
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/04/08 17:00
     */
    public static function changeStatus($params){
        if (!$params['type']) {
            self::setError('缺少类型');
            return false;
        }
        if ($params['type'] == 1) { // 1=管理员重启
            if (!$params['admin_id']) {
                self::setError('您没有权限1');
                return false;
            }
        }else{ // 2=客户重启
            if (!$params['user_id']) {
                self::setError('您没有权限');
                return false;
            }
        }
        if (!$params['to_status_id']) {
            self::setError('缺少变更到状态节点参数');
            return false;
        }
        $to_stauts_id = $params['to_status_id']; // 重启到状态节点ID;
        $adminId = $params['admin_id'];
        $userId = $params['user_id'];
        
        $ticket = TicketModel::where('id',$params['id'])->findOrEmpty();
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        //判断工单是否关闭
        if ($ticket->close_status > 0) { //  关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            self::setError('工单已关闭');
            return false;
        }

        if($params['type'] == 1){//管理员重启
            // $participate = TicketModel::alias('ticket')
            //     ->leftJoin('ticket_dispatch_record dispatch_record', 'ticket.id = dispatch_record.ticket_id')
            //     ->field('ticket.*')
            //     ->group('ticket.id')->where('dispatch_record.admin_id',$adminId)->where('ticket.id',$params['id'])->findOrEmpty();
            // if ($participate->isEmpty()) {
            //     self::setError('您不是参与人');
            //     return false;
            // }

            if(isset($params['api_type']) && $params['api_type'] == 'tenantapi'){//租户端

            }else{
                if($ticket->assignee_id!= $adminId){
                    self::setError('您不是工单处理人');
                    return false;
                }
            }

        }else{
            if($ticket->user_id != $userId){
                self::setError('您不是工单发布员');
                return false;
            }
        }
        
        $old_status_id = $ticket->ticket_status_id; // 工单当前状态ID;

        //判断前后状态是否一致
        if($old_status_id == $to_stauts_id){
            self::setError('前后状态一致');
            return false;
        }

        //判断变更到状态节点是否存在
        $toStatus = TicketStatus::where([['id', '=', $to_stauts_id]])->findOrEmpty(); // 工单当前状态
        if ($toStatus->isEmpty()) { // 工单当前状态的下一个状态
            self::setError('变更到状态节点不存在');
            return false;
        }

        Db::startTrans();
        try {
            $statusTime = TicketStatusTimeLogic::executeTicketStatusTime($ticket->id,$old_status_id,$to_stauts_id,$params['type']==1?$adminId:0,$ticket->tenant_id,$params['type']==1?0:$userId);
            if (!$statusTime) {
                //抛出错误
                throw new \Exception(TicketStatusTimeLogic::getError()??'更新状态节点失败');
            }
            //如果变更到终态
            if($toStatus->is_final == 1){//终态

                //统计工单状态时间
                $ticketStatusTime = TicketStatusTimeLogic::calculateTotalTime($ticket->id,$ticket->tenant_id);
                // 更新工单总处理时间
                $ticket->processing_total_duration = $ticketStatusTime; // 终态
                $ticket->complete_time = time(); // 工单完成时间

                //如果是付费工单并已支付
                if($ticket->payment_amount >0 && $ticket->payment_status == 2 && $ticket->client_confirmed_amount == 1){//付费工单
                    $ticket->close_status = 1; // 关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
                    $ticket->close_text = $params['type']==1? '管理ID：'.$adminId.'变更工单状态,由状态ID:'.$old_status_id.'到状态ID:'.$to_stauts_id.',且已支付费用':'用户ID：'.$userId.'变更工单状态,由状态ID:'.$old_status_id.'到状态ID:'.$to_stauts_id.',且已支付费用'; //工单关闭原因
                }
                // 如果是免费工单
                if($ticket->payment_status == 0){//免费工单
                    $ticket->close_status = 1; // 关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
                    $ticket->close_text = $params['type']==1? '管理ID'.$adminId.'变更工单状态,由状态ID:'.$old_status_id.'到状态ID:'.$to_stauts_id.',且订单为免费':'用户ID：'.$userId.'变更工单状态,由状态ID:'.$old_status_id.'到状态ID:'.$to_stauts_id.',且订单为免费'; //工单关闭原因;
                }

            }
            $ticket->ticket_status_id = $to_stauts_id; // 工单当前状态ID;
            $ticket->latest_processing_time = time(); // 最新处理时间
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }

            

            // 执行工单状态变更的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'status_change', 'api', ['previous_status_id' => $old_status_id]);

            commonTicketService::recordEvent($ticket,TicketEventEnum::CHANGE_STATUS, [
                'user_id' => $params['type'] != 1?$userId:0,//处理人id
                'admin_id' => $params['type']== 1?$adminId:0,//用户id
                'ticket_status_id' => $ticket->ticket_status_id,//工单状态
                'describe'=>  $params['type']==1? '管理ID：'.$adminId.'变更工单状态,由状态ID:'.$old_status_id.'到状态ID:'.$to_stauts_id:'用户ID：'.$userId.'变更工单状态,由状态ID:'.$old_status_id.'到状态ID:'.$to_stauts_id,//工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => $params['browser_type']??1 //浏览器类型 1 手机 2 pc
            ]);
            Db::commit();
            return true;
        }catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }



    /**
     * @notes 重启工单 只要是参与人都可以重启 用户已可以重启
     * @param array $params
     * @return bool
     */
    public static function restart($params){
        if (!$params['type']) {
            self::setError('缺少类型');
            return false;
        }
        if ($params['type'] == 1) { // 1=管理员重启
            if (!$params['admin_id']) {
                self::setError('您没有权限1');
                return false;
            }
        }else{ // 2=客户重启
            if (!$params['user_id']) {
                self::setError('您没有权限');
                return false;
            }
        }
        if (!$params['to_status_id']) {
            self::setError('缺少重启到状态节点参数');
            return false;
        }
        $to_stauts_id = $params['to_status_id']; // 重启到状态节点ID;
        $adminId = $params['admin_id'];
        $userId = $params['user_id'];
        
        $ticket = TicketModel::where('id',$params['id'])->findOrEmpty();
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        //判断工单是否关闭
        if ($ticket->close_status == 0) { //  关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            self::setError('工单正常进行中，不能重启');
            return false;
        }
        //判断工单是否取消
        if ($ticket->cancel_status != 0) { //  关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            self::setError('工单取消状态，不能重启');
            return false;
        }
        $ticketConfig = commonTicketService::getTicketConfig($ticket->tenant_id); // 获取工单配置
        if ($ticketConfig['restart_ticket'] == 0) { // 0=否，1=是
            self::setError('工单配置中，重启工单功能未开启');
            return false;
        }

        if($params['type'] == 1){//管理员重启
            //判断是否是参与人
            if($ticketConfig['assignee_restart'] == 0){ // 0=否，1=是
                self::setError('工单配置中，管理员重启工单功能未开启');
                return false;
            }
            $participate = TicketModel::alias('ticket')
                ->leftJoin('ticket_dispatch_record dispatch_record', 'ticket.id = dispatch_record.ticket_id')
                ->field('ticket.*')
                ->group('ticket.id')->where('dispatch_record.admin_id',$adminId)->where('ticket.id',$params['id'])->findOrEmpty();
            if ($participate->isEmpty()) {
                self::setError('您不是参与人');
                return false;
            }
        }else{
            //判断是否是参与人
            if($ticketConfig['publisher_restart'] == 0){ // 0=否，1=是
                self::setError('工单配置中，客户重启工单功能未开启');
                return false;
            }
            if($ticket->user_id != $userId){
                self::setError('您不是工单发布员');
                return false;
            }
        }
        
        $old_status_id = $ticket->ticket_status_id; // 工单当前状态ID;

        //判断前后状态是否一致
        if($old_status_id == $to_stauts_id){
            self::setError('前后状态一致');
            return false;
        }

        //判断重启到状态节点是否存在
        $toStatus = TicketStatus::where([['id', '=', $to_stauts_id]])->findOrEmpty(); // 工单当前状态
        if ($toStatus->isEmpty()) { // 工单当前状态的下一个状态
            self::setError('重启到状态节点不存在');
            return false;
        }

        Db::startTrans();
        try {
            // 更新工单状态
            $ticket->close_status = 0; // 关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            $ticket->ticket_status_id = $to_stauts_id; // 当前状态的下一个状态
            $ticket->processing_total_duration = 0; // 分配时间
            // 工单完成时间 置空
            $ticket->complete_time = null;
            //重启次数 +1
            $ticket->restart_num = $ticket->restart_num + 1;
            $ticket->latest_processing_time = time(); // 最新处理时间
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }

            $statusTime = TicketStatusTimeLogic::executeTicketStatusTime($ticket->id,$old_status_id,$to_stauts_id,$params['type']==1?$adminId:0,$ticket->tenant_id,$params['type']==1?0:$userId);
            if (!$statusTime) {
                //抛出错误
                throw new \Exception(TicketStatusTimeLogic::getError()??'更新状态节点失败');
            }
            //记录重启状态记录+1
            $newTimeRecord = TicketStatusTime::where('ticket_id',$ticket->id)->where('status_id',$to_stauts_id)->findOrEmpty();
            if ($newTimeRecord->isEmpty()) { // 工单当前状态时间不存在，新增一条
                $newTimeRecord_data = [
                    'tenant_id'    => $ticket->tenant_id,
                    'ticket_id'    => $ticket->id,
                   'status_id'    => $to_stauts_id,
                   'previous_status_id' => $old_status_id,
                   'action_type'   => 1,                    // 操作类型(0=正常流转到达
                   'is_reverted'   => 1,                    // 是否已回退（0=否，1=是）
                   'start_time'   => time(),                    // 开始时间
                   'end_time'   => null,                    // 结束时间
                   'node_total_time'   => 0,                    // 节点总耗时(单位秒)
                   'operator_admin_id'   => $params['type']==1?$adminId:0,                    // 操作员工ID
                   'operator_user_id'   => $params['type']==1?0:$userId,                    // 操作用户ID
                   'restart_num'   => 1,                    // 重启到该状态次数
                   'create_time'   => time(),                    // 创建时间
                   'update_time'   => time(),                    // 更新时间
                   'delete_time'   => null,                    // 删除时间
                ];
                 (new TicketStatusTime())->save($newTimeRecord_data);
            }else{
                $newTimeRecord->restart_num =$ticket->restart_num + 1;
                $newTimeRecord->save();
            }

            // 执行工单状态变更的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'status_change', 'api', ['previous_status_id' => $old_status_id,'notice_type' => NoticeEnum::ORDER_ACCEPT]);

             // // 记录工单创建事件
             commonTicketService::recordEvent($ticket,TicketEventEnum::RESTART_TICKET, [
                'user_id' => $params['type'] != 1?$userId:0,//处理人id
                'admin_id' => $params['type']== 1?$adminId:0,//用户id
                'ticket_status_id' => $ticket->ticket_status_id,//工单状态
                'describe'=>  $params['type']==1? '管理ID：'.$adminId.'重启工单,由状态ID:'.$old_status_id.'到状态ID:'.$to_stauts_id:'用户ID：'.$userId.'重启工单,由状态ID:'.$old_status_id.'到状态ID:'.$to_stauts_id,//工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);
            Db::commit();
            return true;

        }catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 恢复工单 
     * @param array $params
     * @return bool
     */
    public static function restore($params){
        if (!$params['type']) {
            self::setError('缺少类型');
            return false;
        }
        if ($params['type'] == 1) { // 1=管理员重启
            if (!$params['admin_id']) {
                self::setError('您没有权限1');
                return false;
            }
        }else{ // 2=客户重启
            if (!$params['user_id']) {
                self::setError('您没有权限');
                return false;
            }
        }
        $adminId = $params['admin_id'];
        $userId = $params['user_id'];
        
        $ticket = TicketModel::where('id',$params['id'])->findOrEmpty();
        if ($ticket->isEmpty()) {
            self::setError('工单不存在');
            return false;
        }
        //判断工单是否取消
        if ($ticket->cancel_status == 0) { //  关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            self::setError('工单不是取消状态');
            return false;
        }
        $ticketConfig = commonTicketService::getTicketConfig($ticket->tenant_id); // 获取工单配置
        if ($ticketConfig['restore_ticket'] == 0) { // 0=否，1=是
            self::setError('工单配置中，恢复工单功能未开启');
            return false;
        }

        if($params['type'] == 1){//管理员重启
            //判断是否是参与人
            if($ticketConfig['assignee_close'] == 0){ // 0=否，1=是
                self::setError('工单配置中，管理员恢复重启工单功能未开启');
                return false;
            }
            $participate = TicketModel::alias('ticket')
                ->leftJoin('ticket_dispatch_record dispatch_record', 'ticket.id = dispatch_record.ticket_id')
                ->field('ticket.*')
                ->group('ticket.id')->where('dispatch_record.admin_id',$adminId)->where('ticket.id',$params['id'])->findOrEmpty();
            if ($participate->isEmpty()) {
                self::setError('您不是参与人');
                return false;
            }
        }else{
            //判断是否是参与人
            if($ticketConfig['publisher_close'] == 0){ // 0=否，1=是
                self::setError('工单配置中，客户恢复工单功能未开启');
                return false;
            }
            if($ticket->user_id != $userId){
                self::setError('您不是工单发布员');
                return false;
            }
        }
        
        $old_status_id = $ticket->ticket_status_id; // 工单当前状态ID;

        Db::startTrans();
        try {
            // 更新工单状态
            $ticket->close_status = 0; // 关闭状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            $ticket->cancel_status = 0; // 取消状态，0=否，1=系统关闭，2=技术员关闭，3=客户关闭
            $ticket->close_text = $params['type']==1? '管理ID：'.$adminId.'撤销取消工单':'用户ID：'.$userId.'撤销取消工单'; // 当前状态的下一个状态
            $ticket->latest_processing_time = time(); // 最新处理时间
            $result = $ticket->save();
            //这里的result 返回的是 1 或者 0  1 成功  0 失败
            if (!$result) {
                throw new \Exception('工单更新失败');
            }

            $statusTime = TicketStatusTimeLogic::executeTicketStatusTime($ticket->id,0,$old_status_id,$params['type']==1?$adminId:0,$ticket->tenant_id,$params['type']==1?0:$userId);
            if (!$statusTime) {
                //抛出错误
                throw new \Exception(TicketStatusTimeLogic::getError()??'更新状态节点失败');
            }

            // 执行工单状态变更的智能策略
            commonTicketService::executeSmartPolicy($ticket, 'status_change', 'api', ['previous_status_id' => $old_status_id]);

             // // 记录工单创建事件
             commonTicketService::recordEvent($ticket,TicketEventEnum::REVOKE_CANCEL_TICKET, [
                'user_id' => $params['type'] != 1?$userId:0,//处理人id
                'admin_id' => $params['type']== 1?$adminId:0,//用户id
                'ticket_status_id' => $ticket->ticket_status_id,//工单状态
                'describe'=>  $params['type']==1? '管理ID：'.$adminId.'撤销取消工单':'用户ID：'.$userId.'撤销取消工单',//工单描述
                'operation_ip' => request()->ip(),
                'browser_type' => 1 //浏览器类型 1 手机 2 pc
            ]);
            Db::commit();
            return true;

        }catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
}
