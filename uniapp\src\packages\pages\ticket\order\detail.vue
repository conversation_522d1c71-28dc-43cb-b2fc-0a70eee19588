<template>
    <view class="ticket-detail-container">
        <!-- 工单基本信息 -->
        <view class="ticket-detail-header" v-if="!isLoading">
            <view class="ticket-detail-title-row">
                <text class="ticket-detail-title-text">{{ ticketDetail.ticketType.type_name || '上门维护' }}</text>
                <view class="ticket-detail-status-badge ticket-status"
                    :style="'background-color: ' + ticketDetail.ticketStatus.status_color + ';'"
                    v-if="ticketDetail.ticket_status_id">
                    {{ ticketDetail.ticketStatus.status_name || '未处理' }}
                </view>
            </view>
            <view class="ticket-detail-info-grid">
                <view class="ticket-detail-info-item code-urgency">
                    <text class="ticket-detail-info-label">工单编号：</text>
                    <text class="ticket-detail-info-value">{{ ticketDetail.ticket_code }}</text>
                    <!-- 紧急层度 -->
                    <text class="ticket-detail-info-value urgency-tag"
                        :style="'background-color: ' + getColorByIndex(ticketDetail.urgency) + ';'">
                        <dict-value :options="dictData.urgency_degree" :value="ticketDetail.urgency" />
                    </text>
                </view>
                <view class="ticket-detail-info-item">
                    <text class="ticket-detail-info-label">创建时间：</text>
                    <text class="ticket-detail-info-value">{{ ticketDetail.create_time }}</text>
                </view>
                <view class="ticket-detail-info-item" v-if="ticketDetail.processing_total_duration">
                    <text class="ticket-detail-info-label">累计服务时长：</text>
                    <text
                        class="ticket-detail-info-value">{{ formatServiceTime(ticketDetail.processing_total_duration) }}</text>
                </view>
            </view>
            <!-- 工单标题 -->
            <view class="ticket-detail-title">
                <!-- 添加带颜色的点 -->
                <text class="ticket-detail-title-dot"
                    :style="'color: ' + getColorByIndex(ticketDetail.urgency) + ';'">•</text>
                <text class="ticket-detail-title-text" style="font-size: 28rpx;">{{ ticketDetail.ticket_title }}</text>
            </view>
        </view>

        <!-- 加载状态显示 -->
        <view class="loading-container" v-if="isLoading">
            <view class="loading-spinner"></view>
            <text class="loading-text">加载中...</text>
        </view>

        <!-- 主要内容区域 - 只在数据加载完成后显示 -->
        <view v-if="!isLoading">
            <!-- Tab 切换 -->
            <view class="tabs">
                <view class="tab-item" :class="{ active: activeTab === 'detail' }" @click="activeTab = 'detail'">工单详情
                </view>
                <view class="tab-item" :class="{ active: activeTab === 'reply' }" @click="activeTab = 'reply'">
                    留言回复
                    <view v-if="replyList.length" class="tab-badge">
                        {{ replyList.length }}
                    </view>
                </view>
                <view class="tab-item" :class="{ active: activeTab === 'process' }" @click="activeTab = 'process'">
                    处理过程
                    <view v-if="processLogs.length" class="tab-badge">
                        {{ processLogs.length }}
                    </view>
                </view>
            </view>

            <!-- 工单详情 -->
            <view class="ticket-detail-content" v-if="activeTab === 'detail'">
                <!-- 接单师傅信息 -->
                <view class="content-section staff-card" v-if="ticketDetail.assignee_id">
                    <view class="section-title">接单师傅</view>
                    <view class="staff-content">
                        <view class="staff-avatar" v-if="ticketDetail.assignee.avatar">
                            <image :src="ticketDetail.assignee.avatar" mode="aspectFill" class="avatar-image"></image>
                        </view>
                        <view class="staff-avatar" v-else>
                            <text class="avatar-text">{{ ticketDetail.assignee.name.substring(0, 1) }}</text>
                        </view>
                        <view class="staff-detail">
                            <text class="staff-name">{{ ticketDetail.assignee.name }}</text>
                            <text class="staff-phone" @click.stop="callStaff(ticketDetail.assignee.phone)">
                                {{ ticketDetail.assignee.desensitize_phone }} <text class="phone-link">拨打</text>
                            </text>
                        </view>
                    </view>
                    <!-- 师傅认证 -->
                    <!-- <view class="staff-certification" v-if="ticketDetail.assignee.certification">
                        <text class="certification-item">{{ticketDetail.assignee.certification}}</text>
                    </view> -->
                </view>
                <!-- 用户评价展示 -->
                <view class="content-section" v-if="ticketDetail.actions.rated.overall_satisfaction && ticketDetail.actions.status.is_final==1">
                    <view class="section-title">用户评价</view>

                    <!-- 请参考评价弹窗的样式排版内容重新一下 -->
                    <view class="client-rating-content">
                        <view class="client-rating-item flex">
                            <text class="client-rating-label">整体评价</text>
                            <u-rate v-model="ticketDetail.actions.rated.overall_satisfaction" :count="5" size="30"
                                readonly disabled active-color="#ffb300"></u-rate>
                            <text class="rating-poit">{{ ticketDetail.actions.rated.overall_satisfaction }} 分</text>
                        </view>
                        <view class="client-rating-item flex">
                            <text class="client-rating-label">响应时间</text>
                            <u-rate v-model="ticketDetail.actions.rated.response_satisfaction" :count="5" size="30"
                                readonly disabled active-color="#ffb300"></u-rate>
                            <text class="rating-poit">{{ ticketDetail.actions.rated.response_satisfaction }} 分</text>
                        </view>
                        <view class="client-rating-item flex">
                            <text class="client-rating-label">专业技能</text>
                            <u-rate v-model="ticketDetail.actions.rated.skill_satisfaction" :count="5" size="30"
                                readonly disabled active-color="#ffb300"></u-rate>
                            <text class="rating-poit">{{ ticketDetail.actions.rated.skill_satisfaction }} 分</text>
                        </view>
                        <view class="client-rating-item flex">
                            <text class="client-rating-label">评价均值</text>
                            <u-rate v-model="ticketDetail.actions.rated.average" :count="5" size="30" readonly disabled
                                active-color="#ffb300"></u-rate>
                            <text class="rating-poit" style="color: #333333;">{{ ticketDetail.actions.rated.average }}
                                分</text>
                        </view>
                        <view class="mt-[30rpx]" v-if="ticketDetail.actions?.rated?.label?.length > 0">
                            <view class="client-rating-tags-selection">
                                <view v-for="(tag, index) in ticketDetail.actions.rated.label" :key="index"
                                    class="client-rating-tag-option active-tag">
                                    {{ tag }}
                                </view>
                            </view>
                        </view>
                        <view class="mt-[30rpx]" v-if="ticketDetail.actions.rated.user_suggestion">
                            <!-- <text class="client-rating-label mb-[15rpx]">评价建议</text> -->
                            <text
                                class="client-rating-suggestionw">{{ ticketDetail.actions.rated.user_suggestion }}</text>
                        </view>
                    </view>
                </view>
                <!-- 服务信息 -->
                <view class="content-section">
                    <view class="section-title">服务信息</view>
                    <view class="service-info-card">
                        <!-- 基本信息 -->
                        <view class="service-info-row contact-info-row">
                            <view class="service-info-item contact-info-item">
                                <text class="service-info-label">联系人</text>
                                <text class="service-info-value">{{ ticketDetail.user_name || '-' }}</text>
                            </view>
                            <view class="service-info-item contact-info-item">
                                <text class="service-info-label">联系电话</text>
                                <view class="service-info-value-wrapper">
                                    <text class="service-info-value">{{ ticketDetail.user_phone || '-' }}</text>
                                </view>
                            </view>
                        </view>
                        
                        <view class="service-info-row" v-if="ticketDetail.user_email">
                            <view class="service-info-item">
                                <text class="service-info-label">联系邮箱</text>
                                <text class="service-info-value">{{ ticketDetail.user_email }}</text>
                            </view>
                        </view>
                        
                        <view class="service-info-row contact-info-row">
                            <view class="service-info-item contact-info-item">
                                <text class="service-info-label">预约日期</text>
                                <text class="service-info-value">{{ timeFormat(ticketDetail.expected_date) }}</text>
                            </view>
                            <view class="service-info-item contact-info-item">
                                <text class="service-info-label">预约时间</text>
                                <text class="service-info-value">{{ ticketDetail.expected_time_period }}</text>
                            </view>
                        </view>
                        
                        <view class="service-info-row" v-if="ticketDetail.payment_amount > 0">
                            <view class="service-info-item">
                                <text class="service-info-label">付款状态</text>
                                <view class="service-info-value-wrapper">
                                    <text class="service-info-value">
                                        <dict-value :options="dictData.payment_status" :value="ticketDetail.payment_status" />
                                    </text>
                                    <text class="service-info-price">¥{{ ticketDetail.payment_amount }}</text>
                                    <view class="service-info-action" v-if="ticketDetail.close_status==0&&ticketDetail.payment_status==1 && ticketDetail.client_confirmed_amount==0 && ticketDetail.payment_amount>0">
                                        <text class="service-info-btn" @click="showConfirmAmountDialog(ticketDetail.payment_amount)">
                                            <u-icon name="edit-pen" color="#ffffff" size="26"></u-icon>
                                            确认费用
                                        </text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    
                    <!-- 服务标签 -->
                    <view class="service-tags-container" v-if="ticketDetail.service_tags && ticketDetail.service_tags.length">
                        <text class="service-tags-label">服务标签：</text>
                        <view class="service-tags-wrapper">
                            <text class="service-tag" v-for="(tag, index) in ticketDetail.service_tags"
                                :key="index">{{ tag }}</text>
                        </view>
                    </view>

                    <!-- 单位和地址信息 -->
                    <view class="location-card" v-if="ticketDetail.user_company_id || ticketDetail.address">
                        <view class="location-item" v-if="ticketDetail.user_company_id">
                            <text class="location-icon">🏢</text>
                            <text class="location-label">单位：</text>
                            <text class="location-value">{{ ticketDetail.company.company_name }}</text>
                        </view>
                        <view class="location-item" v-if="ticketDetail.address">
                            <text class="location-icon">📍</text>
                            <text class="location-label">地址：</text>
                            <text class="location-value">{{ ticketDetail.address }}</text>
                        </view>
                    </view>
                </view>



                <!-- 详情内容 -->
                <view class="content-section">
                    <view class="section-title">问题描述</view>
                    <view class="ticket-remarks" v-if="ticketDetail.ticket_description">
                        <!-- <view class="remarks-title">备注：</view> -->
                        <view class="remarks-content">{{ ticketDetail.ticket_description }}</view>
                    </view>
                    <view class="ticket-content">
                        <rich-text :nodes="ticketDetail.ticket_content || '暂无描述'"></rich-text>
                        <view class="images-container"
                            v-if="ticketDetail.ticket_images && ticketDetail.ticket_images.length">
                            <image v-for="(img, index) in ticketDetail.ticket_images" :key="index" :src="img"
                                mode="aspectFill" @click="previewImage(img, ticketDetail.ticket_images)"
                                class="content-image"></image>
                        </view>
                    </view>
                    <view class="related-tickets" v-if="ticketDetail.related_ticket_id">
                        <text class="related-label">相关工单</text>
                        <view class="related-list">
                            <text class="related-ticket" @click="goToDetail(ticketDetail.related_ticket_id)">{{
                                ticketDetail.relatedTicket.ticket_title }}</text>
                        </view>
                    </view>
                </view>


                <!-- AI分析结果 -->
                <!-- AI智能分析结果 -->
                <AiAnalysisResult :ticketDetail="ticketDetail" :aiAnalysis="aiAnalysis" />

                <!-- 设备信息 -->
                <view class="content-section" v-if="ticketDetail.product_id">
                    <view class="section-title">设备信息</view>
                    <view class="device-info">
                        <view class="device-info-item">
                            <text class="device-info-label">设备名称</text>
                            <text class="device-info-value">{{ ticketDetail.product.name || '-' }}</text>
                        </view>
                        <view class="device-info-item">
                            <text class="device-info-label">品牌</text>
                            <text class="device-info-value">{{ ticketDetail.product.brand || '-' }}</text>
                        </view>
                        <view class="device-info-item">
                            <text class="device-info-label">设备编码</text>
                            <text class="device-info-value">{{ ticketDetail.product.code || '-' }}</text>
                        </view>
                        <view class="device-info-item">
                            <text class="device-info-label">设备型号</text>
                            <text class="device-info-value">{{ ticketDetail.product.model || '-' }}</text>
                        </view>
                        <view class="device-info-item">
                            <text class="device-info-label">标签</text>
                            <text class="device-info-value">{{ ticketDetail.product.label || '-' }}</text>
                        </view>
                        <view class="device-info-item">
                            <text class="device-info-label">购买日期</text>
                            <text class="device-info-value">{{
                                timeFormat(ticketDetail.product.maintenance_end_time,'yyyy-mm-dd hh:MM:ss',true)}}</text>
                        </view>
                        <view class="device-info-item">
                            <text class="device-info-label">保修状态</text>
                            <text class="device-info-value">
                                <dict-value :options="dictData.maintenance_status"
                                    :value="ticketDetail.product_maintenance_state" />
                            </text>
                        </view>
                        <view class="device-info-item">
                            <text class="device-info-label">保修到期</text>
                            <text
                                class="device-info-value">{{ ticketDetail.product.maintenance_end_time ? timeFormat(ticketDetail.product.maintenance_end_time, 'yyyy-mm-dd hh:MM:ss'):'长期'}}</text>
                        </view>
                    </view>
                </view>

                <!-- 服务要求 -->
                <!-- <view class="content-section" v-if="ticketDetail.service_requirements && ticketDetail.service_requirements.length">
                    <view class="section-title">服务要求</view>
                    <view class="requirements-list">
                        <view class="requirement-item" v-for="(req, index) in ticketDetail.service_requirements" :key="index">
                            <text class="requirement-dot">•</text>
                            <text class="requirement-text">{{req}}</text>
                        </view>
                    </view>
                </view> -->

                <!-- 额外信息 -->
                <!-- <view class="content-section" v-if="ticketDetail.additional_info">
                    <view class="section-title">其他信息</view>
                    <view class="additional-info">
                        <view class="additional-item" v-if="ticketDetail.additional_info.usage_frequency">
                            <text class="additional-label">使用频率</text>
                            <text class="additional-value">{{ticketDetail.additional_info.usage_frequency}}</text>
                        </view>
                        <view class="additional-item" v-if="ticketDetail.additional_info.fault_frequency">
                            <text class="additional-label">故障频率</text>
                            <text class="additional-value">{{ticketDetail.additional_info.fault_frequency}}</text>
                        </view>
                        <view class="additional-item" v-if="ticketDetail.additional_info.environmental_factors">
                            <text class="additional-label">使用环境</text>
                            <text class="additional-value">{{ticketDetail.additional_info.environmental_factors}}</text>
                        </view>
                    </view>
                    
                </view> -->

                <!-- 发票信息 -->
                <!-- <view class="content-section" v-if="ticketDetail.invoice_required">
                    <view class="section-title">发票信息</view>
                    <view class="invoice-info">
                        <view class="invoice-item">
                            <text class="invoice-label">发票类型</text>
                            <text class="invoice-value">{{ticketDetail.invoice_type || '普通发票'}}</text>
                        </view>
                        <view class="invoice-item">
                            <text class="invoice-label">支付方式</text>
                            <text class="invoice-value">{{ticketDetail.payment_method || '线下支付'}}</text>
                        </view>
                    </view>
                </view> -->
                
                <!-- 工单补充信息 -->
                <view class="content-section" v-if="ticketDetail.allocate_ticket_time||ticketDetail.latest_processing_time||ticketDetail.complete_time">
                    <view class="section-title">补充信息</view>
                    <view class="info-grid">
                        <!-- <view class="info-grid-item">
                            <text class="info-grid-label">联系人</text>
                            <text class="info-grid-value">{{ ticketDetail.user_name || '-' }}</text>
                        </view>
                        <view class="info-grid-item">
                            <text class="info-grid-label">联系电话</text>
                            <text class="info-grid-value">{{ ticketDetail.user_phone || '-' }}</text>
                        </view> -->
                        <!-- <view class="info-grid-item" v-if="ticketDetail.user_email">
                            <text class="info-grid-label">联系邮箱</text>
                            <text class="info-grid-value">{{ ticketDetail.user_email }}</text>
                        </view> -->
                        <view class="info-grid-item" v-if="ticketDetail.allocate_ticket_time">
                            <text class="info-grid-label">派单时间</text>
                            <text class="info-grid-value">{{ timeFormat(ticketDetail.allocate_ticket_time, 'yyyy-mm-dd hh:MM:ss')}}</text>
                        </view>
                        <view class="info-grid-item" v-if="ticketDetail.latest_processing_time">
                            <text class="info-grid-label">最近处理时间</text>
                            <text class="info-grid-value">{{ timeFormat(ticketDetail.latest_processing_time, 'yyyy-mm-dd hh:MM:ss')}}</text>
                        </view>
                        <view class="info-grid-item" v-if="ticketDetail.complete_time">
                            <text class="info-grid-label">完成时间</text>
                            <text class="info-grid-value">{{ timeFormat(ticketDetail.complete_time, 'yyyy-mm-dd hh:MM:ss')}}</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 留言回复列表 -->
            <view class="ticket-detail-reply-list" v-if="activeTab === 'reply'">
                <view v-if="replyList.length === 0" class="ticket-detail-empty-tips">
                    <image src="/static/images/empty.png" mode="aspectFit" class="ticket-detail-empty-icon">
                    </image>
                    <text>暂无留言回复</text>
                    <button class="add-reply-button" v-if="ticketDetail.close_status == 0"
                        @click="openReplyDialog(0, '')">新增留言</button>
                </view>
                <view v-else v-for="(item, index) in replyList" :key="index" class="ticket-detail-reply-item"
                    :class="{ 'ticket-detail-staff-reply': item.reply_type != 1 }">
                    <view class="ticket-detail-reply-header">
                        <view class="ticket-detail-reply-user-info" v-if="item.reply_type == 1">
                            <!-- 用户 -->
                            <image v-if="item.avatar" :src="item.avatar" class="ticket-detail-reply-avatar"></image>
                            <view v-else class="ticket-detail-reply-avatar ticket-detail-default-avatar">
                                <text>{{ item.nickname.substring(0, 1) }}</text>
                            </view>
                            <text class="ticket-detail-reply-name">{{ item.nickname }}</text>
                            <view class="ticket-detail-reply-badge ticket-detail-user-badge">用户</view>
                        </view>
                        <view class="ticket-detail-reply-user-info" v-else>
                            <!-- 管理员 -->
                            <image v-if="item.admin_avatar" :src="item.admin_avatar" class="ticket-detail-reply-avatar">
                            </image>
                            <view v-else class="ticket-detail-reply-avatar ticket-detail-default-avatar">
                                <text>{{ item.admin_name.substring(0, 1) }}</text>
                            </view>
                            <text class="ticket-detail-reply-name">{{ item.admin_name }}</text>
                            <view class="ticket-detail-reply-badge" v-if="item.jobs_name">{{ item.jobs_name }}</view>
                            <view class="ticket-detail-reply-badge ticket-detail-user-badge" v-else>系统</view>
                        </view>
                        <text class="ticket-detail-reply-time">{{ timeFormat(item.reply_time, 'yyyy-mm-dd hh:MM:ss')
                            }}</text>
                    </view>

                    <!-- 当 item.reply_id 有值时，显示回复的富文本内容 -->
                    <view v-if="item.reply_id" class="ticket-detail-reply-reply-content">
                        <!-- <view class="reply-reply-header">
                            <text class="reply-reply-label">回复内容：</text>
                        </view> -->
                        <rich-text :nodes="item.reply_reply_content"></rich-text>
                    </view>
                    <view class="ticket-detail-reply-content">
                        <!-- 原回复内容富文本显示 -->
                        <rich-text :nodes="item.reply_content"></rich-text>
                    </view>
                    <view class="ticket-detail-reply-attachments" v-if="item.reply_images && item.reply_images.length">
                        <image v-for="(img, imgIndex) in item.reply_images" :key="imgIndex" :src="img" mode="aspectFill"
                            @click="previewImage(img, item.reply_images)" class="ticket-detail-reply-image"></image>
                    </view>
                    <!-- 新增留言回复按钮 -->
                    <view class="reply-btn-container" v-if="ticketDetail.close_status == 0 && item.id != 'local'">
                        <button class="reply-to-message-btn"
                            @click="openReplyDialog(item.id, item.reply_content)">回复</button>
                    </view>
                    <!-- 是否已读 -->
                    <!-- <view class="ticket-detail-reply-status" v-if="!item.is_read && item.reply_type != 1">
                        <view class="ticket-detail-unread-badge">未读</view>
                    </view> -->
                </view>
            </view>

            <!-- 处理过程 -->
            <view class="ticket-detail-process-logs" v-if="activeTab === 'process'">
                <view v-if="processLogs.length === 0" class="ticket-detail-empty-tips">
                    <image src="/static/images/empty.png" mode="aspectFit" class="ticket-detail-empty-icon">
                    </image>
                    <text>暂无处理记录</text>
                </view>
                <view v-else class="ticket-detail-timeline">
                    <view v-for="(log, index) in processLogs" :key="index" class="ticket-detail-timeline-item"
                        :class="{ 'ticket-detail-system-log': log.jobs_id }">
                        <view class="ticket-detail-timeline-dot" :class="{ 'ticket-detail-system-dot': log.jobs_id }">
                        </view>
                        <view class="ticket-detail-timeline-content">
                            <view class="ticket-detail-log-header">
                                <text class="ticket-detail-log-title">
                                    <dict-value :options="dictData.ticket_event_type" :value="log.event_type" />
                                </text>
                                <text class="ticket-detail-log-time">{{ log.create_time }}</text>
                            </view>
                            <view class="ticket-detail-log-operator" v-if="log.admin_id">
                                <text class="ticket-detail-operator-label">操作人：</text>
                                <text class="ticket-detail-operator-name">{{log.admin_name}}</text>
                                <view class="ticket-detail-operator-type" >{{log.jobs_name|| '系统'}}</view>
                            </view>
                            <view class="ticket-detail-log-operator" v-else>
                                <text class="ticket-detail-operator-label">操作人：</text>
                                <view class="ticket-detail-operator-type-user" >我</view>
                            </view>

                            <!-- <view class="ticket-detail-log-remark" v-if="log.user_approval_id>0">{{log.user_approval_id}}</view>
                            <view class="ticket-detail-log-remark" v-if="log.reject_reason_id>0">{{log.reject_reason_id}}</view> -->

                            <!-- <view class="ticket-detail-log-location" v-if="log.location">
                                <text class="ticket-detail-location-label">位置：</text>
                                <text class="ticket-detail-location-value">{{log.location}}</text>
                            </view> -->
                        </view>
                    </view>
                </view>
            </view>
            
        </view>

        <!-- 底部操作区 -->
        <view class="ticket-detail-footer" v-if="!isLoading">
            <view class="foot-fix-action-section">
                <view class="action-btn" v-if="ticketDetail.close_status == 0 && ticketDetail.cancel_status == 0 && ticketDetail.actions.status.is_final == 0 && !ticketDetail.complete_time && ticketDetail.actions.config.publisher_close">
                    <button @click.stop="showCancelDialog()" >
                        取消工单
                    </button>
                </view>
                <view class="action-btn" v-if="ticketDetail.close_status > 0 && ticketDetail.cancel_status > 0 && ticketDetail.actions.status.is_final == 0 &&!ticketDetail.complete_time && ticketDetail.actions.config.publisher_close&&ticketDetail.actions.config.restore_ticket">
                    <button @click.stop="showRestoreDialog()" >
                        撤回取消
                    </button>
                </view>
                <view class="action-btn" v-if="ticketDetail.close_status > 0 && ticketDetail.cancel_status == 0 && ticketDetail.actions.config.restart_ticket && ticketDetail.actions.config.publisher_restart">
                    <button @click.stop="showRestartDialog()" >
                        重启工单
                    </button>
                </view>
                <view class="action-btn" v-if="ticketDetail.close_status==0&&ticketDetail.actions.status.is_final==0">
                    <button @click.stop="reminderAction()" >
                        催单
                    </button>
                </view>
                <view class="action-btn" v-if="ticketDetail.actions.status.is_final==1 && ticketDetail.rating_status == 0">
                    <button @click.stop="openRatingPopup()" >
                        评价服务
                    </button>
                </view>
                <!-- <view class="action-btn" v-if="ticketDetail.close_status == 0">
                    <button @click.stop="showChangeUrgencyDialog()" >
                        变更优先级
                    </button>
                </view> -->
            </view>

        </view>
                <!-- 组件 -->
        <view>
            <!-- 状态节点组件 -->
            <TicketStatusSelector
                :title="popStatusTitle"
                :show="showStatusSelector"
                :statusList="selectorStatusList"
                @confirm="handleConfirmStatusSelector"
                @close-popup="showStatusSelector = false"
            />
        </view>

        <!-- 评价服务弹窗 -->
        <u-popup v-model="ratingPopupVisible" mode="center" border-radius="16" width="650rpx"
            :close-on-click-overlay="false">
            <view class="client-rating-popup">
                <view class="client-rating-header">
                    <text class="client-rating-title">服务评价</text>
                    <text class="client-rating-close" @click="ratingPopupVisible = false">×</text>
                </view>
                <view class="client-rating-content">
                    <view class="">
                        <view class="client-rating-item mb-[15rpx] flex">
                            <text class="client-rating-label">整体</text>
                            <u-rate @change="changeRating()" v-model="ratingData.overall_satisfaction" :count="5"
                                size="30" active-color="#ffb300"></u-rate>
                            <text class="rating-poit">{{ ratingData.overall_satisfaction }} 分</text>
                        </view>
                        <view class="client-rating-item mb-[15rpx] flex">
                            <text class="client-rating-label">响应时间</text>
                            <u-rate @change="changeRating()" v-model="ratingData.response_satisfaction" :count="5"
                                size="30" active-color="#ffb300"></u-rate>
                            <text class="rating-poit">{{ ratingData.response_satisfaction }} 分</text>
                        </view>
                        <view class="client-rating-item mb-[15rpx] flex">
                            <text class="client-rating-label">专业技能</text>
                            <u-rate @change="changeRating()" v-model="ratingData.skill_satisfaction" :count="5"
                                size="30" active-color="#ffb300"></u-rate>
                            <text class="rating-poit">{{ ratingData.skill_satisfaction }} 分</text>
                        </view>
                        <view class="client-rating-item mb-[15rpx] flex">
                            <text class="client-rating-label">评价均值</text>
                            <u-rate v-model="ratingData.average" :count="5" size="30" readonly disabled
                                active-color="#333333"></u-rate>
                            <text class="rating-poit" style="color: #333333;">{{ ratingData.average }} 分</text>
                        </view>
                    </view>
                    <view class="mt-[30rpx]">
                        <view class="client-rating-tags-selection">
                            <view v-for="(tag, index) in ratingTags" :key="index"
                                :class="['client-rating-tag-option', ratingData.label.includes(tag) ? 'active-tag' : '']"
                                @click="toggleRatingTag(tag)">
                                {{ tag }}
                            </view>
                        </view>
                    </view>
                    <view class="mt-[30rpx]">
                        <text class="client-rating-label mb-[15rpx]">
                            <sapn class="rating-requried">*</sapn>评价建议
                        </text>
                        <textarea class="client-rating-suggestion" v-model="ratingData.user_suggestion"
                            placeholder="您的建议将帮助我们提升服务质量"></textarea>
                    </view>
                </view>
                <view class="client-rating-footer">
                    <button class="client-rating-cancel" @click="ratingPopupVisible = false">取消</button>
                    <button class="client-rating-submit" @click="submitRating">提交评价</button>
                </view>
            </view>
        </u-popup>

        <!-- 取消工单弹窗 -->
        <u-popup v-model="cancelPopupVisible" mode="center" border-radius="16" width="650rpx"
            :close-on-click-overlay="false">
            <view class="client-cancel-popup">
                <view class="client-cancel-header">
                    <text class="client-cancel-title">取消工单</text>
                    <text class="client-cancel-close" @click="cancelPopupVisible = false">×</text>
                </view>
                <view class="client-cancel-content">
                    <view class="client-cancel-tips">
                        <u-icon name="warning-fill" color="#ff9900" size="36"></u-icon>
                        <text>确定要取消该工单吗？取消后将无法恢复</text>
                    </view>
                    <view class="client-cancel-reason-select">
                        <text class="client-reason-label">请选择取消原因：</text>
                        <view class="client-reason-options">
                            <radio-group @change="cancelReasonRadioChange">
                                <view class="reason-radio-list">
                                    <label class="reason-radio-item" v-for="(reason, index) in cancelReasons"
                                        :key="reason">
                                        <radio :value="reason" :color="$theme.primaryColor" :checked="index === currentReason" />
                                        <text class="reason-text">{{ reason }}</text>
                                    </label>
                                </view>
                            </radio-group>
                            <!-- 当选择其他原因时显示输入框 -->
                            <view v-if="showCustomReasonInput" class="custom-reason-container">
                                <textarea class="client-custom-reason-input" v-model="customReason"
                                    placeholder="请输入具体取消原因" @input="handleCustomReasonInput"></textarea>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="client-cancel-footer">
                    <button class="client-cancel-btn-secondary" @click="cancelPopupVisible = false">返回</button>
                    <button class="client-cancel-btn-primary" @click="confirmCancelTicket">确认取消</button>
                </view>
            </view>
        </u-popup>

        <!-- 留言回复工单弹窗 -->
        <view class="popup-mask" v-if="showReplyPopup" @click="closeReplyDialog"></view>
        <view class="popup-content" v-if="showReplyPopup">
            <view class="reply-popup">
                <view class="reply-header">
                    <text class="reply-title">留言回复工单</text>
                    <text class="reply-close" @click="closeReplyDialog">×</text>
                </view>
                <view class="reply-content">
                    <uni-forms class="form-container" ref="formRef">
                        <uni-forms-item label="" prop="replyContent">
                            <uni-easyinput type="textarea" v-model="replyForm.replyContent"  :primaryColor="$theme.primaryColor" placeholder="请输入内容..." />
                        </uni-forms-item>
                        <!-- 使用 u-upload 组件替代自定义上传 -->
                        <uni-forms-item label="" prop="replyImages">
                            <view class="upload-container">
                                <view class="upload-list">
                                    <view class="upload-item" v-for="(item, index) in replyForm.replyImages"
                                        :key="index">
                                        <image class="upload-image" :src="item" mode="aspectFill"></image>
                                        <view class="upload-delete" @click="deleteImage(index)">×</view>
                                    </view>
                                    <view class="upload-add" @click="fetchChooseImage"
                                        v-if="replyForm.replyImages.length < 3">
                                        <view class="add-icon">+</view>
                                        <view class="add-text">添加图片</view>
                                    </view>
                                </view>
                            </view>
                        </uni-forms-item>
                    </uni-forms>
                </view>

                <view class="reply-footer">
                    <button class="reply-btn-secondary" @click="closeReplyDialog">取消</button>
                    <button class="reply-btn-primary" @click="submitReply">提交回复</button>
                </view>
            </view>
        </view>
        <!-- 使用新的公共消息详情弹窗组件 -->
        <MessageDetailPopupWrapper ref="messageDetailPopupRef" />
    </view>
</template>

<script>
import { getTicketDetail, cancelTicket, restartTicket, changeUrgencyTicket, reminderTicket, rateTicket, 
    confirmAmountTicket, getTicketTimeline, getTicketReplies, replyTicket,getTicketStatusList,restoreTicket } from '@/api/ticket/ticket'
import { timeFormat, formatServiceTime, getColorByIndex } from '@/utils/util'
import { callPhone } from '@/utils/customFn';
import { useDictOptions, useDictData } from '@/hooks/useDictOptions'
import DictValue from '@/components/dict-value/index.vue'
import { cancelReasons, ratingTags } from '@/config/ticketConfig';
import { uploadImage } from '@/api/app'
import { useUserStore } from '@/stores/user'; // 引入用户信息的 Store
import MessageDetailPopupWrapper from '@/components/custom/MessageDetailPopupWrapper.vue';
import AiAnalysisResult from './components/AiAnalysisResult.vue'; // 引入新组件
import TicketStatusSelector from '@/components/custom/TicketStatusSelector.vue';
const userStore = useUserStore();
export default {
    components: {
        DictValue, // 注册组件
        AiAnalysisResult,
        MessageDetailPopupWrapper,
        TicketStatusSelector
    },
    data() {
        const systemInfo = uni.getSystemInfoSync();
        return {
            cancelReasons,
            ratingTags,
            dictData: {},
            ticketId: '',
            ticketDetail: {
                // 更完整的初始化数据结构，防止渲染时报错
                ticekt_title: '',
                ticket_status_id: 0,
                ticketStatus: { status_name: '', status_color: '' },
                ticket_code: '',

                id: 27,
                tenant_id: 1,
                ticket_code: "T202504280004",
                product_id: 0,
                product_maintenance_state: 1,
                user_region_code: 451002,
                receiver_admin_id: 0,
                project_id: 0,
                urgency: 2,
                ticket_type_id: 2,
                template_id: 3,
                assignee_id: 0,
                dept_id: 0,
                user_id: 3,
                user_name: '用户41652493',
                user_phone: "15888522220",
                user_email: "",
                latitude: "23.878527",
                longitude: "106.619849",
                user_company_id: 1,
                address: "广西百色市右江区龙景街道东洲101号555",
                source_type: 1,
                related_ticket_id: 0,
                ticket_title: "第二条测试巡检工单",
                ticket_description: "寻访",
                ticket_content: "",
                ticket_images: [
                    "http://child.word.ops.hf/uploads/images/20250428/20250428153538fa2505781.png",
                    "http://child.word.ops.hf/uploads/images/20250428/202504281535424535a0995.png",
                    "http://child.word.ops.hf/uploads/images/20250428/2025042815354773c355220.png"
                ],
                allocation_status: 0,
                payment_status: 0,
                payment_amount: "0.00",
                client_confirmed_amount: 0,
                ticket_status_id: 1,
                audit_status: 0,
                workflow_audit_id: 0,
                reply_status: 0,
                view_status: 0,
                first_receive_time: 0,
                allocate_ticket_time: null,
                latest_processing_time: null,
                ticket_action: "",
                close_status: 0,
                close_text: null,
                complete_time: null,
                processing_total_duration: null,
                is_timeout: 0,
                delete_operator_id: 0,
                delete_ip: null,
                expected_date: 1745856000,
                expected_time_period: "14:00-16:00",
                create_time: "2025-04-28 15:35:55",
                update_time: "2025-04-28 15:35:55",
                delete_time: null,
                ticketType: {
                    id: 2,
                    tenant_id: 1,
                    type_name: "保修工单",
                    sort: 1,
                    is_enabled: 1,
                    create_time: "2025-02-28 15:50:38",
                    update_time: null,
                    delete_time: null
                },
                ticketStatus: {
                    id: 1,
                    tenant_id: 1,
                    status_name: "待处理",
                    status_code: "PENDING",
                    is_initial: 1,
                    is_final: 0,
                    pause_timing: 0,
                    status_color: "#FFA555",
                    sort: 0,
                    create_time: "2025-02-28 15:50:38",
                    update_time: null,
                    delete_time: null
                },
                assignee: null,
                actions: {
                    canCancel: true,
                    canConfirmAmount: false,
                    canClose: false,
                    canReminder: true,
                    canRate: false,
                    rated: []
                },

                images: [],
                service_tags: [],
                title: '工单详情',
                type_name: '',
                priority: '',
                ticket_no: '',
                created_at: new Date().getTime(),
                id: ''
            },
            // 标签页
            activeTab: 'detail',
            // 处理过程数据
            processLogs: [],
            // 回复列表数据
            replyList: [],
            // 运行的客户端
            isApp: systemInfo.platform === 'android' || systemInfo.platform === 'ios',
            isH5: systemInfo.platform === 'h5',
            isWechat: systemInfo.platform === 'mp-weixin',
            // 取消工单相关
            cancelPopupVisible: false,
            currentReason: 0,
            showCustomReasonInput: false,
            customReason: '',

            // 评价相关
            ratingPopupVisible: false,
            ratingData: {
                average: 5,
                overall_satisfaction: 5,
                response_satisfaction: 5,
                skill_satisfaction: 5,
                label: ratingTags.slice(0, 3),
                user_suggestion: ''
            },
            // 回复工单相关
            showReplyPopup: false,
            replyId: 0,//回复的ID
            replyReplyContent: '',//回复的回复内容
            // 修改为对象形式
            replyForm: {
                replyContent: '',
                replyImages: []
            },
            imagesSubmitList: [],
            imagesIdSubmitList: [],
            isLoading: true,

            initStatusList: [],
            // 状态节点选择器
            popStatusTitle: '选择状态节点',
            popStatusType: 'status',
            showStatusSelector: false,
            selectorStatusList: [],
            selectedStatus: { id: 0, status_name: '请选择', sort: 0 }
        }
    },
    computed: {
        showBottomActions() {
            return (
                (this.ticketDetail.status === 'pending' && this.ticketDetail.canCancel) ||
                (this.ticketDetail.status === 'completed' && this.ticketDetail.canRate && !this.ticketDetail.rated)
            );
        },
        // 计算平均评分
        averageRating() {
            if (!this.ratingForm) return 5;
            const sum = this.ratingForm.efficiency + this.ratingForm.attitude + this.ratingForm.professional;
            return Math.round(sum / 3);
        },
        // 解析AI分析数据
        aiAnalysis() {
            // 初始化空对象，设置默认值
            const defaultAnalysis = {
                root_cause: '',
                complexity_level: '',
                estimated_fix_time: '',
                required_skills: [],
                suggested_solution: '',
                parts_needed: [],
                prevention_advice: ''
            };
            
            // 如果没有AI分析数据，返回默认值
            if (!this.ticketDetail.ai_analysis) {
                return defaultAnalysis;
            }
            
            try {
                // 如果ai_analysis是字符串，尝试解析它
                let analysis = this.ticketDetail.ai_analysis;
                if (typeof analysis === 'string') {
                    try {
                        analysis = JSON.parse(analysis);
                    } catch (e) {
                        console.error('解析AI分析数据失败:', e);
                        return defaultAnalysis;
                    }
                }
                
                // 合并默认值和解析后的值，确保所有字段都有值
                return {
                    ...defaultAnalysis,
                    ...analysis,
                    // 确保数组字段始终是数组
                    required_skills: Array.isArray(analysis.required_skills) ? analysis.required_skills : [],
                    parts_needed: Array.isArray(analysis.parts_needed) ? analysis.parts_needed : []
                };
            } catch (error) {
                console.error('处理AI分析数据失败:', error);
                return defaultAnalysis;
            }
        }
    },
    onLoad(options) {

        // 设置页面标题
        uni.setNavigationBarTitle({
            title: '工单详情'
        });
        //判断是否携带ID 如果没有携带ID 则跳转到工单列表
        if (!options || !options.id) {
            uni.navigateBack({
                delta: 1
            });
            return;
        } else {
            this.ticketId = options.id;
        }
        //默认标签
        if (!options.tab) {
            this.activeTab = 'detail';
        } else {
            this.activeTab = options.tab;
        }
        // 初始化字典数据
        this.initDictData();
        //读取工单详细
        this.loadTicketDetail();
        this.loadStatusList();
    },
    methods: {
        timeFormat,
        formatServiceTime,
        getColorByIndex,
        // 初始化字典数据
        async initDictData() {
            try {
                const { dictData } = useDictData('urgency_degree,source_type,is_read,ticket_event_type,allocation_status,payment_status,user_confirmed_amount,audit_status,reply_status,view_status,close_type,close_status,is_timeout,maintenance_status,service_period');
                this.dictData = dictData;
            } catch (error) {
                console.error('Failed to load dict data:', error);
            }
        },
        async loadStatusList() {
            try {
                const list = await getTicketStatusList();
                console.log('statusList:', list);
                this.initStatusList = list;
            } catch (error) {
                this.$u.toast(error || '加载状态列表失败');
            }
        },
        // 加载工单详情
        async loadTicketDetail() {
            console.log('Loading ticket detail for ID:', this.ticketId);
            this.isLoading = true;
            uni.showLoading({ title: '加载中...' });
            try {
                const response = await getTicketDetail(this.ticketId);

                console.log('Ticket Detail Response:', response);
                this.ticketDetail = response;

                // 设置页面标题为工单名称
                if (this.ticketDetail.ticket_title) {
                    uni.setNavigationBarTitle({
                        title: this.ticketDetail.ticket_title.length > 10
                            ? this.ticketDetail.ticket_title.substring(0, 10) + '...'
                            : this.ticketDetail.ticket_title
                    });
                }
                // 确保一定执行这些方法 获取工单回复评论 工单处理过程
                this.$nextTick(() => {
                    this.loadProcessLogs();
                    this.loadReplyList();
                    // 强制触发视图更新
                    this.ticketDetail = { ...this.ticketDetail };
                });

            } catch (error) {
                console.error('Failed to load ticket detail:', error);
                this.$u.toast(error || '加载工单详情失败');
                
            } finally {
                uni.hideLoading();
                this.isLoading = false;
            }
        },
        // 重启工单
        showRestartDialog() {
            uni.showModal({
                title: '提示',
                content: `您确定重启该工单？`,
                confirmText: '确定',
                cancelText: '取消',
                cancelColor: this.$theme.minorColor, // 取消按钮文字颜色（默认#000）
                confirmColor: this.$theme.primaryColor, // 确认按钮颜色（默认#3CC51F）
                success: (res) => {
                    if (res.confirm) {
                        this.showRestartStatusSelector();
                    }
                }
            });
        },
        // 弹出选择重启回到的节点
        showRestartStatusSelector() {
            //操作的工单
            let  currentTicket = this.ticketDetail;
            const currentStatusId = currentTicket.ticket_status_id;// 当前工单的状态id
            // 获取 initStatusList 中 移除id = currentStatusId 得到的新数组 赋值给组件使用的 selectorStatusList
            this.selectorStatusList = this.initStatusList.filter(item => item.id !== currentStatusId);
            console.log('selectorStatusList:', this.selectorStatusList);
            //弹出选择状态组件
            this.popStatusTitle = '请选择重启到的状态';
            this.popStatusType = 'restart';
            this.showStatusSelector = true; 
        },
        // 请求重启工单接口
        async confirmRestartTicket() {
            if (!this.selectedStatus.id) {
                this.$u.toast('请选择重启到的状态');
                return;
            }
            try {
                await restartTicket({
                    id: this.ticketId,
                    to_status_id: this.selectedStatus.id,
                    type: 2
                });
                // 将该工单重启到的状态不确定 各操作按钮可操作变化较大 更新整个列表到回到顶部
                this.$u.toast('重启成功');
                
                this.ticketDetail.processing_total_duration = 0; // 1 是初始状态的 ID
                this.ticketDetail.complete_time = ''; // 完成时间
                this.ticketDetail.close_status = 0; // 关闭状态
                this.ticketDetail.ticket_status_id = this.selectedStatus.id; // 1 是初始状态的 ID
                this.ticketDetail.status_name = this.selectedStatus.status_name; // 1 是初始状态的 ID
                this.ticketDetail.status_color = this.selectedStatus.status_color; // 1 是初始状态的 ID
                this.ticketDetail.restart_num = this.ticketDetail.restart_num + 1; // 重启次数
            }catch (err) {
                this.$u.toast(err);
            }
        },
        handleConfirmStatusSelector(selectedStatus) { 
            console.log('确认选择的状态：', selectedStatus);
            this.selectedStatus = selectedStatus;
            if (this.popStatusType === 'restart') {//重启
                this.showStatusSelector = false; // 关闭选择器
                this.confirmRestartTicket();
            }else if (this.popStatusType ==='change') {//变更
                this.showStatusSelector = false; // 关闭选择器
                // this.confirmChangeStatus();
            }
        },
        // 确认金额
        showConfirmAmountDialog() {
            uni.showModal({
                title: '提示',
                content: '请确认该工单需支付的费用' + this.ticketDetail.payment_amount + '元？',
                confirmText: '确定',
                cancelText: '取消',
                cancelColor: this.$theme.minorColor, // 取消按钮文字颜色（默认#000）
                confirmColor: this.$theme.primaryColor, // 确认按钮颜色（默认#3CC51F）
                success: (res) => {
                    if (res.confirm) {
                        this.confirmAmountTicket();
                    }
                }
            });
        },
        async confirmAmountTicket() {
            try {
                // 这里原代码可能有误，假设存在 confirmAmountTicket 方法
                await confirmAmountTicket({
                    id: this.ticketId,
                });
                this.ticketDetail.client_confirmed_amount = 1;//本地设为1
                this.$u.toast('操作成功');
            } catch (err) {
                this.$u.toast(err || '确认失败，请重试');
            }
        },
        goToDetail(id) {
            uni.navigateTo({
                url: `/packages/pages/ticket/order/detail?id=${id}`
            })
        },
        // 加载工单事件列表数据
        async loadProcessLogs() {
            try {
                const response = await getTicketTimeline({ ticket_id: this.ticketId });
                console.log('Process Logs Response:', response);
                this.processLogs = response;
            } catch (error) {
                console.error('Failed to load process logs:', error);
            }
        },
        // 加载回复列表数据
        async loadReplyList() {
            try {
                const response = await getTicketReplies({ ticket_id: this.ticketId });
                console.log('Reply List Response:', response);
                // {
                //     id: 1,
                //     content: '您好，已收到您的工单，我们将尽快处理。请问您的设备具体是什么型号？',
                //     sender: '客服小李',
                //     sender_id: 'STAFF003245',
                //     sender_type: 'staff',
                //     sender_avatar: 'https://picsum.photos/id/1001/300/300',
                //     timestamp: new Date(Date.now() - 86400000 * 2.5).getTime(),
                //     attachments: [],
                //     is_read: true
                // }
                this.replyList = response;
            } catch (error) {
                console.error('Failed to load reply list:', error);
            }
        },
        // 电话呼叫方法
        callStaff(phone) {
            callPhone(phone);
        },
        // 催单
        async reminderAction() {
            console.log('Reminder action triggered for ticket ID:', this.ticketId);
            try {
                await reminderTicket({
                    id: this.ticketId,
                });
                // this.ticketDetail.actions.canReminder = false;//本地设为false
                this.$u.toast('催单成功');
            } catch (err) {
                console.error('Failed to reminder ticket:', err);
                this.$u.toast(err || '催单失败，请重试');
            }
        },
        //变更紧急度
        showChangeUrgencyDialog() {
            let urgencyArr =this.dictData.urgency_degree;
            // urgencyArr 的结构为 [{id:1,name:'普通'},{id:2,name:'紧急'},{id:3,name:'非常紧急'}]
            // 转换为 ['普通','紧急','非常紧急']
            let useUrgencyArr = urgencyArr.map(item => item.name);
            console.log(useUrgencyArr)
            uni.showActionSheet({
                itemList: useUrgencyArr,
                success: (res) => {
                    if (res.tapIndex !== -1) {
                        // res.tapIndex 是用户点击的选项的索引，从 0 开始
                        //根据 res.tapIndex 结合useUrgencyArr 数组和原始数组 urgencyArr, 获取选项的 value 值
                        let selectId = urgencyArr[res.tapIndex].value;
                        console.log('用户点击的选项的id:', selectId);
                        this.changeUrgency(selectId);
                        // this.changeUrgency(res.tapIndex);
                    }
                }
            });  
        },
        async changeUrgency(urgency) {
            try {
                await changeUrgencyTicket({
                    id: this.ticketId,
                    urgency,
                    mode: 1
                }); 
                this.ticketDetail.urgency = urgency;
                this.$u.toast('操作成功');
                // this.loadTicketDetail();
            } catch (err) {
                this.$u.toast(err || '变更失败，请重试');
            }
        },
        //撤回取消
        showRestoreDialog(id) {
            uni.showModal({
                title: '提示',
                content: '确定要撤回取消该工单吗？',
                confirmText: '确定',
                cancelText: '取消',
                cancelColor: this.$theme.minorColor, // 取消按钮文字颜色（默认#000）
                confirmColor: this.$theme.primaryColor, // 确认按钮颜色（默认#3CC51F）
                success: (res) => {
                    if (res.confirm) {
                        this.confirmRestoreTicket();
                    }
                }
            });
        },
        async confirmRestoreTicket() {
            try {
                await restoreTicket({
                    id: this.ticketId,
                    type: 2
                });
                this.$u.toast('操作成功');
                //工单取消状态改为0
                this.ticketDetail.cancel_status = 0; // 0 是取消状态的 ID
                this.ticketDetail.close_status = 0; // 0 是关闭状态的 ID
            } catch (err) {
                this.$u.toast(err || '操作失败，请重试');
            }
        },
        // 取消工单相关
        showCancelDialog() {
            this.cancelPopupVisible = true;
            this.currentReason = 0;
        },
        cancelReasonRadioChange(evt) {
            console.log('cancelReasonRadioChange called with event:', evt);
            let selectedValue;
            if (this.isApp || this.isWechat) {
                selectedValue = evt.detail.value;
            } else if (this.isH5) {
                selectedValue = evt.target.value;
            }
            console.log('cancelReasonRadioChange called with selectedValue:', selectedValue);
            for (let i = 0; i < this.cancelReasons.length; i++) {
                if (this.cancelReasons[i] === selectedValue) {
                    this.currentReason = i;
                    break;
                }
            }
            this.showCustomReasonInput = selectedValue === '其他原因';
            if (!this.showCustomReasonInput) {
                this.customReason = '';
            }
            console.log('currentReason after change:', this.currentReason);
        },
        handleCustomReasonInput() {
            // 可以在这里添加输入验证等逻辑
        },
        async confirmCancelTicket() {
            let reason;
            if (this.cancelReasons[this.currentReason] === '其他原因') {
                if (!this.customReason.trim()) {
                    this.$u.toast('请输入具体取消原因');
                    return;
                }
                reason = this.customReason;
            } else {
                reason = this.cancelReasons[this.currentReason];
            }

            if (!reason) {
                this.$u.toast('取消原因错误')
                return
            }
            try {
                await cancelTicket({
                    id: this.ticketId,
                    reason,
                    close_status: 3
                });
                this.ticketDetail.close_status = 3;//本地设为3
                this.ticketDetail.cancel_status = 3;//本地设为3
                this.$u.toast('工单已取消');
                this.cancelPopupVisible = false;
                // this.loadTicketDetail();
            } catch (err) {
                this.$u.toast(err || '取消失败，请重试');
            }
        },
        // 评价相关 ratingPopupVisible openRatingPopup
        openRatingPopup() {
            this.ratingPopupVisible = true
            this.ratingData = {
                average: 5,
                overall_satisfaction: 5,
                response_satisfaction: 5,
                skill_satisfaction: 5,
                label: ['效率高', '态度好', '专业'],
                user_suggestion: ''
            };
        },
        toggleRatingTag(tag) {
            if (this.ratingData.label.includes(tag)) {
                this.ratingData.label = this.ratingData.label.filter(t => t !== tag);
            } else {
                this.ratingData.label.push(tag);
            }
        },
        changeRating() {
            const total = this.ratingData.overall_satisfaction + this.ratingData.response_satisfaction + this.ratingData.skill_satisfaction;
            this.ratingData.average = Math.round(total / 3 * 10) / 10;
        },
        async submitRating() {
            if (this.ratingData.average < 1) {
                this.$u.toast('请至少选择一颗星')
                return
            }
            try {
                await rateTicket({
                    id: this.ticketId,
                    average: this.ratingData.average,
                    overall_satisfaction: this.ratingData.overall_satisfaction,
                    response_satisfaction: this.ratingData.response_satisfaction,
                    skill_satisfaction: this.ratingData.skill_satisfaction,
                    label: this.ratingData.label,
                    user_suggestion: this.ratingData.user_suggestion
                });
                this.$u.toast('感谢您的评价');
                this.ratingPopupVisible = false;
                this.ticketDetail.rating_status = 1;//标记已评分
                this.ticketDetail.actions.rated = {
                    average: this.ratingData.average,
                    overall_satisfaction: this.ratingData.overall_satisfaction,
                    response_satisfaction: this.ratingData.response_satisfaction,
                    skill_satisfaction: this.ratingData.skill_satisfaction,
                    label: this.ratingData.label,
                    user_suggestion: this.ratingData.user_suggestion
                };//本地设为true
            } catch (err) {
                this.$u.toast(err || '评价失败，请重试');
            }
        },

        // 预览图片
        previewImage(current, images) {
            uni.previewImage({
                urls: images,
                current: current
            })
        },

        // 图片上传
        async uploadImageIng(file) {
            uni.showLoading({
                title: '正在上传中...'
            })
            try {
                const image = await uploadImage(file, userStore.temToken)
                uni.hideLoading()
                console.log(image)
                if (image) {
                    this.imagesSubmitList.push(image.url)
                    this.imagesIdSubmitList.push(image.id)
                }
            } catch (error) {
                uni.hideLoading()
                this.$u.toast(error || '图片上传失败')
                
            }
        },
        // 选择图片
        async fetchChooseImage() {
            uni.chooseImage({
                count: 3 - this.replyForm.replyImages.length,
                sizeType: ['compressed'],
                sourceType: ['album', 'camera'],
                success: async (res) => {
                    // 这里添加到本地数组
                    this.replyForm.replyImages = [...this.replyForm.replyImages, ...res.tempFilePaths];
                    // 在实际应用中，这里应该上传图片到服务器
                    for (let i = 0; i < res.tempFilePaths.length; i++) {
                        try {
                            this.uploadImageIng(res.tempFilePaths[i])
                            console.log('上传成功');
                        } catch (error) {
                            console.error('上传失败', error)
                        }
                    }
                    this.$u.toast('图片添加成功')
                },
                fail: (err) => {
                    console.error('选择图片失败:', err);
                    this.$u.toast('选择图片失败')
                }
            });
        },
        // 删除图片
        deleteImage(index) {
            this.replyForm.replyImages.splice(index, 1);
            //接口删除后端保存图片
            if (this.imagesIdSubmitList[index]) {
                // 这里应该调用删除图片的接口
                console.log('删除图片ID:', this.imagesIdSubmitList[index]);
                this.imagesIdSubmitList.splice(index, 1);
                //调接口删除
                // TODO: 实现删除图片的逻辑
            }
            this.$u.toast('图片删除成功')
        },

        // 提交回复
        async submitReply() {
            if (!this.replyForm.replyContent.trim()) {
                this.$u.toast('请输入回复内容')
                return;
            }

            uni.showLoading();
            //replyTicket
            try {
                let rest = await replyTicket({
                    id: this.ticketId,
                    reply_content: this.replyForm.replyContent,
                    reply_images: this.imagesSubmitList,
                    reply_id: this.replyId,
                    reply_type: 1
                });
                console.log('Reply submitted:', rest);
                // 创建回复数据
                const newReply = {
                    id: 'local',
                    reply_content: this.replyForm.replyContent,
                    reply_id: this.replyId,
                    reply_images: this.replyForm.replyImages,
                    avatar: userStore.userInfo.avatar,
                    nickname: userStore.userInfo.nickname, // 假设当前用户
                    replyer_id: userStore.userInfo.id, // 假设当前用户ID,
                    reply_type: 1,
                    replier_type: 'user',
                    sender_avatar: 'https://picsum.photos/id/1002/300/300',
                    reply_time: Date.now(),
                    reply_reply_content: this.replyReplyContent, // 假设回复的回复内容为空
                };
                // 添加到回复列表
                this.replyList.unshift(newReply);

                // 重置表单
                this.replyForm.replyContent = '';
                this.replyForm.replyImages = [];

                uni.hideLoading();
                this.$u.toast('回复成功')

                this.closeReplyDialog();
                this.activeTab = 'reply'; // 切换到回复选项卡
            } catch (error) {
                uni.hideLoading();
                console.error('Failed to reply ticket:', error);
                this.$u.toast(error || '回复失败，请重试')
            }
        },
        // 打开回复工单弹窗
        openReplyDialog(replyId, content) {
            this.replyForm.replyContent = ''; // 重置回复内容
            this.replyForm.replyImages = []; // 重置图片
            this.imagesSubmitList = []; // 重置图片
            this.imagesIdSubmitList = []; // 重置图片
            this.replyId = replyId; // 重置回复ID
            this.replyReplyContent = content; // 重置回复的回复内容
            this.showReplyPopup = true
        },
        // 关闭回复工单弹窗
        closeReplyDialog() {
            this.showReplyPopup = false
        },

    }
}
</script>

<style>
.ticket-detail-container {
    min-height: 100vh;
    background-color: #f5f7fa;
    display: flex;
    flex-direction: column;
    padding-bottom: 120rpx;
    /* 为底部按钮留出空间 */
    box-sizing: border-box;
    position: relative;
}

/* 重写头部样式，确保类名唯一 */
.ticket-detail-header {
    /* background: linear-gradient(135deg, v-bind('$theme.primaryColor') 0%, v-bind('$theme.navBgColor') 100%); */
    padding: 40rpx 30rpx;
    color: #fff;
    width: 100%;
    box-sizing: border-box;
    z-index: 10;

    background: url(../../../../static/images/user/my_topbg.png),
        linear-gradient(90deg, v-bind('$theme.primaryColor'), v-bind('$theme.navBgColor'));
    background-repeat: no-repeat;
    background-position: bottom;
    background-size: 100%;
}

.ticket-detail-title {
    display: flex;
    align-items: center;
    /* 垂直居中对齐 */
}

.ticket-detail-title-dot {
    font-size: 36rpx;
    /* 调整点的大小 */
    margin-right: 10rpx;
    /* 点与标题之间的间距 */
}

.ticket-detail-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    width: 100%;
}

.ticket-detail-title-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
}

.ticket-detail-status-badge {
    padding: 6rpx 20rpx;
    border-radius: 30rpx;
    font-size: 24rpx;
    background-color: rgba(255, 255, 255, 0.3);
}

.ticket-detail-info-grid {
    display: flex;
    flex-wrap: wrap;
    font-size: 24rpx;
    opacity: 0.9;
}

.ticket-detail-info-item {
    margin-right: 20rpx;
    margin-bottom: 10rpx;
}

.ticket-detail-info-label {
    font-weight: bold;
}

.ticket-detail-info-value {
    margin-left: 10rpx;
}

/* 状态颜色 - 使用唯一类名 */
.ticket-status {
    color: #fff;
}

.code-urgency {
    display: flex;
    align-items: center;
}

.urgency-tag {
    padding: 2rpx 10rpx;
    border-radius: 6rpx;
    margin-left: 10rpx;
}

/* 重写底部样式，确保类名唯一 */
.ticket-detail-footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    display: flex;
    justify-content: space-around;
    padding: 20rpx;
    border-top: 1rpx solid #eee;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    z-index: 100;
    width: 100%;
    box-sizing: border-box;
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.foot-fix-action-section {
    display: flex;
    flex-direction: row-reverse; /* 让子元素从底部往上排列 */
    flex-wrap: wrap-reverse; 
    gap: 10rpx; 
    padding: 15rpx; 
    background-color: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    box-sizing: border-box;
    padding-bottom: calc(25rpx + env(safe-area-inset-bottom));
}

.action-btn {
    flex-shrink: 0; 
}

.action-btn button {
    min-width: 120rpx; /* 减小最小宽度 */
    height: 60rpx; /* 减小高度 */
    line-height: 60rpx; 
    text-align: center;
    border-radius: 30rpx; /* 相应调整圆角 */
    font-size: 24rpx; /* 减小字体大小 */
    border: none;
    background-color: v-bind('$theme.primaryColor');
    color: #fff;
    white-space: nowrap; 
}

/* 加载状态样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;
    margin: 20rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid v-bind('$theme.primaryColor');
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
}

.loading-text {
    font-size: 28rpx;
    color: #666;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 详情内容样式 */
.content-section {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    color: #333;
    position: relative;
    padding-left: 20rpx;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 6rpx;
    width: 6rpx;
    height: 26rpx;
    background-color: v-bind('$theme.primaryColor');
    border-radius: 3rpx;
}

.ticket-content {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
    margin-top: 20rpx;

}

.images-container {
    margin-top: 20rpx;
    display: flex;
    flex-wrap: wrap;
}

.content-image {
    width: 180rpx;
    height: 180rpx;
    margin-right: 12rpx;
    margin-bottom: 12rpx;
    border-radius: 8rpx;
}

/* //相关工单 */
.related-label{
    font-weight: 600;
}
.related-list{
    color: v-bind('$theme.primaryColor');
}

/* 补充信息网格样式 */
.info-grid {
    display: flex;
    flex-wrap: wrap;
    background-color: #f9f9f9;
    border-radius: 8rpx;
    padding: 20rpx;
}

.info-grid-item {
    width: 50%;
    margin-bottom: 24rpx;
    padding: 0 10rpx;
    box-sizing: border-box;
}

.info-grid-label {
    font-size: 26rpx;
    color: #999;
    display: block;
    margin-bottom: 8rpx;
}

.info-grid-value {
    font-size: 28rpx;
    color: #333;
}

.info-grid-tag {
    font-size: 26rpx;
    color: #333;
    margin-left: 20rpx;
    background-color: #f0f0f0;
    padding: 2rpx 12rpx;
    border-radius: 16rpx;
}

.info-grid-price {
    font-size: 28rpx;
    font-weight: 600;
    color: v-bind('$theme.primaryColor');
    margin: 0 20rpx;
}

.location-info {
    display: flex;
    align-items: center;
    margin: 20rpx 0;
    padding: 15rpx;
    background-color: #f9f9f9;
    border-radius: 8rpx;
}

.location-label {
    font-size: 26rpx;
    color: #999;
}

.location-text {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

.location-detail {
    display: flex;
    align-items: center;
    margin-top: 10rpx;
    padding: 15rpx;
    background-color: #f9f9f9;
    border-radius: 8rpx;
}

.location-icon {
    font-size: 32rpx;
    margin-right: 10rpx;
}

.detail-label {
    font-size: 26rpx;
    color: #666;
    flex: 1;
}

/* 设备信息样式 */
.device-info {
    background-color: #f9f9f9;
    border-radius: 8rpx;
    padding: 20rpx;
}

.device-info-item {
    display: flex;
    margin-bottom: 16rpx;
}

.device-info-item:last-child {
    margin-bottom: 0;
}

.device-info-label {
    width: 180rpx;
    font-size: 26rpx;
    color: #666;
}

.device-info-value {
    flex: 1;
    font-size: 26rpx;
    color: #333;
}

/* Tab 切换样式 */
.tabs {
    display: flex;
    background-color: #fff;
    border-bottom: 1rpx solid #eee;
    position: relative;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 24rpx 0;
    font-size: 28rpx;
    color: #666;
    position: relative;
    transition: all 0.3s;
}

.tab-item .tab-badge {
    display: inline-block;
    min-width: 24rpx;
    height: 24rpx;
    line-height: 24rpx;
    padding: 0 8rpx;
    background-color: #999;
    color: #fff;
    font-size: 20rpx;
    border-radius: 12rpx;
    vertical-align: middle;
}

.tab-item.active {
    color: v-bind('$theme.primaryColor');
    font-weight: bold;
}

.tab-item.active .tab-badge {
    /* 灰色 */
    /* background-color: #999; */
}

.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60rpx;
    height: 6rpx;
    background: linear-gradient(90deg, v-bind('$theme.primaryColor') 0%, v-bind('$theme.navBgColor') 100%);
    border-radius: 6rpx;
}

/* 回复列表样式 */
.ticket-detail-reply-list {
    padding: 25rpx;
    background-color: #f7f8fa;
}

.ticket-detail-reply-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
    position: relative;
    border-left: 4rpx solid transparent;
    transition: all 0.3s ease;
}

.ticket-detail-reply-item:active {
    transform: scale(0.99);
    opacity: 0.9;
}

.ticket-detail-staff-reply {
    background-color: #f0f8ff;
    border-left: 4rpx solid v-bind('$theme.primaryColor');
}

.ticket-detail-reply-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
    padding-bottom: 12rpx;
}

.ticket-detail-reply-user-info {
    display: flex;
    align-items: center;
}

.ticket-detail-reply-avatar {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    margin-right: 12rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.ticket-detail-default-avatar {
    background-color: v-bind('$theme.primaryColor');
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 28rpx;
}

.ticket-detail-reply-name {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    /* 新增样式 */
    max-width: 200rpx; /* 根据实际情况调整宽度 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ticket-detail-reply-badge {
    padding: 2rpx 12rpx;
    border-radius: 20rpx;
    background-color: v-bind('$theme.primaryColor');
    color: #fff;
    font-size: 20rpx;
    margin-left: 12rpx;
}

.ticket-detail-user-badge {
    background-color: #ff9500;
}

.ticket-detail-reply-time {
    font-size: 22rpx;
    color: #999;
}

.ticket-detail-reply-content {
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
    margin-bottom: 12rpx;
    word-break: break-all;
}

.ticket-detail-reply-reply-content {
    margin-top: 15rpx;
    padding: 15rpx;
    background-color: #f5f5f5;
    border-radius: 8rpx;
}

.ticket-detail-reply-attachments {
    display: flex;
    flex-wrap: wrap;
    gap: 10rpx;
    margin-top: 16rpx;
}

.ticket-detail-reply-image {
    width: 180rpx;
    height: 180rpx;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ticket-detail-reply-image:active {
    transform: scale(0.97);
}

.reply-btn-container {
    display: flex;
    justify-content: flex-end;
    /* 让子元素靠右排列 */
    margin-top: 15rpx;
    /* 保持与原按钮的上边距一致 */
}

.reply-to-message-btn {
    background-color: v-bind('$theme.primaryColor');
    color: #fff;
    border-radius: 20rpx;
    font-size: 24rpx;
    height: 50rpx;
    line-height: 50rpx;
    min-width: 120rpx;
    padding: 0 10rpx;
    display: inline-block;
    margin: 0;
}

.ticket-detail-empty-tips {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 0;
    color: #999;
    font-size: 28rpx;
}

.ticket-detail-empty-icon {
    width: 180rpx;
    height: 180rpx;
    margin-bottom: 20rpx;
    opacity: 0.6;
}

.add-reply-button {
    margin-top: 20rpx;
    background-color: v-bind('$theme.primaryColor');
    color: #fff;
    border-radius: 40rpx;
    font-size: 28rpx;
    height: 80rpx;
    line-height: 80rpx;
    min-width: 200rpx;
}
.rating-requried{color: red;margin-right: 10rpx;}
/* 处理过程样式 */
.ticket-detail-process-logs {
    padding: 20rpx;
    background-color: #f7f8fa;
}

.ticket-detail-timeline {
    position: relative;
    padding: 10rpx 0;
}

.ticket-detail-timeline:before {
    content: '';
    position: absolute;
    left: 9rpx;
    top: 0;
    bottom: 0;
    width: 2rpx;
    background: linear-gradient(180deg, v-bind('$theme.primaryColor'), rgba(255, 187, 41, 0.1));
    z-index: 0;
}

.ticket-detail-timeline-item {
    position: relative;
    padding-left: 36rpx;
    padding-bottom: 30rpx;
    z-index: 1;
}

.ticket-detail-timeline-item:last-child {
    padding-bottom: 10rpx;
}

.ticket-detail-timeline-dot {
    position: absolute;
    left: 0;
    top: 10rpx;
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    background: linear-gradient(135deg,  v-bind('$theme.primaryColor') 0%, v-bind('$theme.navBgColor') 100%);
    box-shadow: 0 2rpx 6rpx rgba(41, 121, 255, 0.3);
    z-index: 2;
}

.ticket-detail-system-log {
    opacity: 0.8;
}

.ticket-detail-system-dot {
    background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
    box-shadow: 0 2rpx 6rpx rgba(117, 117, 117, 0.3);
}

.ticket-detail-timeline-content {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.ticket-detail-log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
    padding-bottom: 8rpx;
}

.ticket-detail-log-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
}

.ticket-detail-log-time {
    font-size: 22rpx;
    color: #999;
}

.ticket-detail-log-operator {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #666;
    margin-bottom: 8rpx;
}

.ticket-detail-operator-label {
    color: #999;
}

.ticket-detail-operator-name {
    color: #333;
    font-weight: 500;
    margin: 0 4rpx;
}

.ticket-detail-operator-type {
    font-size: 20rpx;
    color: #fff;
    background-color: #9e9e9e;
    padding: 2rpx 12rpx;
    border-radius: 16rpx;
    margin-left: 8rpx;
}
.ticket-detail-operator-type-user {
    font-size: 20rpx;
    color: #fff;
    background-color: v-bind('$theme.primaryColor');
    padding: 2rpx 12rpx;
    border-radius: 16rpx;
    margin-left: 8rpx;
}

/* 服务类型卡片样式 */
.service-card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.appointment-info {
    margin-top: 20rpx;
    margin-bottom: 20rpx;
}

.appointment-item {
    display: flex;
    align-items: baseline;
    margin-bottom: 10rpx;
    justify-self: start;
}

.appointment-label {
    font-size: 26rpx;
    color: #999;
    margin-right: 10rpx;
}

.appointment-value {
    font-size: 28rpx;
    color: #333;
    margin-left: 10rpx;
}

.appointment-tag {
    font-size: 26rpx;
    color: #333;
    margin-left: 20rpx;
}

.appointment-price {
    font-size: 28rpx;
    font-weight: 600;
    color: v-bind('$theme.primaryColor');
    margin: 0 20rpx;
}


.set-amount-btn{
    margin-left: 20rpx;
    background-color: v-bind('$theme.primaryColor');
    color: #fff;
    padding: 4rpx 16rpx;
    border-radius: 16rpx;
    font-size: 24rpx;
}

.service-tags {
    display: flex;
    flex-wrap: wrap;
}

.service-tag {
    padding: 6rpx 20rpx;
    border-radius: 30rpx;
    background-color: #f0f0f0;
    font-size: 24rpx;
    color: #666;
    margin-right: 10rpx;
    margin-bottom: 10rpx;
}

/* 接单师傅卡片样式 */
.staff-card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.staff-content {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
}

.staff-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;
    overflow: hidden;
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-text {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
}

.staff-detail {
    display: flex;
    flex-direction: column;
}

.staff-name {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 6rpx;
}

.staff-phone {
    font-size: 24rpx;
    color: #666;
}

.phone-link {
    color: v-bind('$theme.primaryColor');
    margin-left: 10rpx;
}

/* 备注样式 */
.ticket-remarks {
    background-color: #fff8e1;
    border-radius: 8rpx;
    padding: 16rpx;
}

.remarks-content {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
}

/* 满意度评价弹窗样式 */
.client-rating-popup {
    width: 650rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
}

.client-rating-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #eee;
}

.client-rating-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
}

.client-rating-close {
    font-size: 40rpx;
    color: #999;
    padding: 0 10rpx;
}

.client-rating-content {
    padding: 30rpx;
    max-height: 700rpx;
    overflow-y: auto;
}

.client-rating-label {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 15rpx;
}

.client-rating-tags-selection {
    display: flex;
    flex-wrap: wrap;
}

.client-rating-tag-option {
    padding: 6rpx 20rpx;
    border-radius: 30rpx;
    background-color: #f0f0f0;
    margin-right: 10rpx;
    margin-bottom: 10rpx;
    font-size: 24rpx;
    color: #333;
}

.client-rating-tag-option.active-tag {
    background-color: v-bind('$theme.primaryColor');
    color: #fff;
}

.client-rating-suggestion {
    width: 100%;
    height: 200rpx;
    border: 1rpx solid #eee;
    border-radius: 8rpx;
    padding: 15rpx;
    font-size: 28rpx;
    box-sizing: border-box;
}

.client-rating-footer {
    display: flex;
    border-top: 1rpx solid #eee;
}

.client-rating-cancel,
.client-rating-submit {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 28rpx;
    border: none;
    border-radius: 0;
    margin: 20rpx;
}

.client-rating-cancel {
    background-color: v-bind('$theme.minorColor');
    color: #666;
}

.client-rating-submit {
    background-color: v-bind('$theme.primaryColor');
    color: #fff;
}

/* 取消工单弹窗样式 */
.client-cancel-popup {
    width: 650rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.client-cancel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    background-color: #f9f9f9;
}

.client-cancel-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
}

.client-cancel-close {
    font-size: 40rpx;
    color: #999;
    padding: 0 10rpx;
    height: 60rpx;
    width: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s;
}

.client-cancel-close:active {
    background-color: #f0f0f0;
}

.client-cancel-content {
    padding: 30rpx;
}

.client-cancel-tips {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 30rpx;
    text-align: center;
    background-color: #fff9f0;
    padding: 20rpx;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.client-cancel-tips text {
    margin-left: 10rpx;
}

.client-cancel-reason-select {
    margin-top: 20rpx;
}

.client-reason-label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
    font-weight: 500;
}

.client-reason-options {
    display: flex;
    flex-direction: column;
}

.reason-radio-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
}

.reason-radio-item {
    display: flex;
    align-items: center;
    padding: 10rpx 0;
}

.reason-text {
    margin-left: 10rpx;
    font-size: 28rpx;
    color: #333;
}

.custom-reason-container {
    margin-top: 20rpx;
}

.client-custom-reason-input {
    width: 100%;
    height: 160rpx;
    border: 1rpx solid #e0e0e0;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
    background-color: #f9f9f9;
}

.client-cancel-footer {
    display: flex;
    border-top: 1rpx solid #f0f0f0;
    padding: 20rpx;
}

.client-cancel-btn-secondary,
.client-cancel-btn-primary {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 28rpx;
    border: none;
    margin: 0 10rpx;
    border-radius: 40rpx;
}

.client-cancel-btn-secondary {
    background-color: #f0f0f0;
    color: #666;
}

.client-cancel-btn-primary {
    background-color: v-bind('$theme.primaryColor');
    color: #fff;
}

/* 回复工单弹窗样式 */
.reply-popup {
    width: 650rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
}

.reply-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #eee;
}

.reply-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
}

.reply-close {
    font-size: 40rpx;
    color: #999;
    padding: 0 10rpx;
}

.reply-content {
    padding: 30rpx;
}

.upload-container {
    margin-top: 20rpx;
}

.upload-list {
    display: flex;
    flex-wrap: wrap;
}

.upload-item,
.upload-add {
    width: 150rpx;
    height: 150rpx;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    position: relative;
    border-radius: 12rpx;
    overflow: hidden;
}

.upload-image {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
}

.upload-delete {
    position: absolute;
    /* 修改为图片容器内的右上角 */
    right: 8rpx;
    top: 8rpx;
    width: 40rpx;
    height: 40rpx;
    background-color: rgba(0, 0, 0, 0.4);
    color: #fff;
    border-radius: 50%;
    text-align: center;
    line-height: 40rpx;
    font-size: 28rpx;
    z-index: 10;
}

.upload-add {
    border: 2rpx dashed #dcdfe6;
    border-radius: 12rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fafafa;
    transition: all 0.3s;
}

.upload-add:active {
    background-color: #f0f2f5;
    border-color: #c0c4cc;
}

.add-icon {
    font-size: 60rpx;
    color: #c0c4cc;
    margin-bottom: 10rpx;
}

.add-text {
    font-size: 24rpx;
    color: #909399;
}

.reply-footer {
    display: flex;
    border-top: 1rpx solid #eee;
}

.reply-btn-secondary,
.reply-btn-primary {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 28rpx;
    border: none;
    margin: 20rpx;
}

.reply-btn-secondary {
    background-color: v-bind('$theme.minorColor');
    color: #666;
}

.reply-btn-primary {
    background-color: v-bind('$theme.primaryColor');
    color: #fff;
}

/* 弹窗通用样式 */
.popup-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.popup-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    width: 90%;
    max-width: 650rpx;
}

/* 服务信息卡片样式 */
.service-info-card {
    background-color: #fff;
    border-radius: 8rpx;
    margin-bottom: 20rpx;
}

.service-info-row {
    padding: 16rpx 0;
    border-bottom: 1rpx solid #f5f5f5;
}

.service-info-row:last-child {
    border-bottom: none;
}

.service-info-item {
    display: flex;
    flex-direction: column;
    padding: 0 10rpx;
}

.service-info-label {
    font-size: 26rpx;
    color: #999;
    margin-bottom: 8rpx;
}

.service-info-value {
    font-size: 28rpx;
    color: #333;
}

.service-info-value-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

.service-info-badge {
    font-size: 24rpx;
    color: #333;
    background-color: #f0f0f0;
    padding: 2rpx 16rpx;
    border-radius: 20rpx;
    margin-left: 16rpx;
    white-space: nowrap;
}

.service-info-price {
    font-size: 28rpx;
    font-weight: 600;
    color: v-bind('$theme.primaryColor');
    margin: 0 16rpx;
    white-space: nowrap;
}

.service-info-action {
    margin-left: auto;
}

.service-info-btn {
    background-color: v-bind('$theme.primaryColor');
    color: #fff;
    padding: 4rpx 16rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    display: inline-flex;
    align-items: center;
}

/* 服务标签容器 */
.service-tags-container {
    margin-top: 20rpx;
    display: flex;
    padding: 10rpx 0;
}

.service-tags-label {
    font-size: 26rpx;
    color: #999;
    white-space: nowrap;
    margin-right: 10rpx;
}

.service-tags-wrapper {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
}

.service-tag {
    padding: 4rpx 20rpx;
    border-radius: 30rpx;
    background-color: #f0f0f0;
    font-size: 24rpx;
    color: #666;
    margin-right: 10rpx;
    margin-bottom: 10rpx;
}

/* 位置信息卡片 */
.location-card {
    margin-top: 20rpx;
    background-color: #f9f9f9;
    border-radius: 8rpx;
    padding: 16rpx;
}

.location-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
}

.location-item:last-child {
    margin-bottom: 0;
}

.location-icon {
    font-size: 32rpx;
    margin-right: 10rpx;
}

.location-label {
    font-size: 26rpx;
    color: #999;
    margin-right: 8rpx;
    white-space: nowrap;
}

.location-value {
    font-size: 28rpx;
    color: #333;
    flex: 1;
    word-break: break-all;
}

.contact-info-row {
    display: flex;
    justify-content: space-between;
}

.contact-info-item {
    flex: 1;
}

.service-info-phone-btn {
    color: v-bind('$theme.primaryColor');
    font-size: 24rpx;
    margin-left: 16rpx;
    padding: 2rpx 12rpx;
    border: 1rpx solid v-bind('$theme.primaryColor');
    border-radius: 20rpx;
    white-space: nowrap;
}

</style>